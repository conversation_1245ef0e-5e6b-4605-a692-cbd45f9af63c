#!/usr/bin/env python3
"""
Batch Pile Detection Processing Script

This script uses Papermill to execute pile detection notebooks across multiple sites,
ground segmentation methods, and architectures for comprehensive comparison.

Author: Preeta<PERSON> Balijepalli
Date: December 2024
Project: Energy Inspection 3D
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
import pandas as pd

try:
    import papermill as pm
    PAPERMILL_AVAILABLE = True
except ImportError:
    PAPERMILL_AVAILABLE = False
    print("Papermill not available. Install with: pip install papermill")

try:
    import mlflow
    MLFLOW_AVAILABLE = True
except ImportError:
    MLFLOW_AVAILABLE = False
    print("MLflow not available. Install with: pip install mlflow")


class BatchPileDetectionProcessor:
    """
    Batch processor for pile detection notebooks using Papermill.
    """
    
    def __init__(self, config_file: str = None):
        """
        Initialize the batch processor.
        
        Parameters:
        -----------
        config_file : str, optional
            Path to configuration file
        """
        self.config = self.load_config(config_file)
        self.results = []
        self.start_time = datetime.now()
        
        # Setup directories
        self.setup_directories()
        
        # Initialize MLflow if available
        if MLFLOW_AVAILABLE and self.config.get('enable_mlflow', True):
            mlflow.set_experiment(self.config.get('mlflow_experiment', 'pile_detection_batch'))
    
    def load_config(self, config_file: str = None) -> Dict[str, Any]:
        """
        Load configuration from file or use defaults.
        
        Parameters:
        -----------
        config_file : str, optional
            Path to configuration file
            
        Returns:
        --------
        config : dict
            Configuration dictionary
        """
        default_config = {
            'sites': ['site_001', 'site_002', 'site_003'],
            'ground_methods': ['csf', 'pmf', 'ransac'],
            'architectures': ['dgcnn', 'pointnet_plus_plus'],
            'pile_types': ['i_section', 'c_section'],
            'confidence_thresholds': [0.7],
            'enable_cross_validation': True,
            'cv_folds': 5,
            'enable_enhanced_analysis': True,
            'enable_mlflow': True,
            'notebooks_dir': 'notebooks/pile_detection',
            'output_dir': 'output_runs/pile_detection',
            'batch_output_dir': 'output_runs/batch_processing',
            'input_data_dir': 'data/processed/ground_segmentation',
            'models_dir': 'models',
            'mlflow_experiment': 'pile_detection_batch',
            'parallel_execution': False,
            'max_workers': 4
        }
        
        if config_file and Path(config_file).exists():
            with open(config_file, 'r') as f:
                user_config = json.load(f)
            default_config.update(user_config)
        
        return default_config
    
    def setup_directories(self):
        """Setup required directories."""
        for dir_key in ['output_dir', 'batch_output_dir']:
            dir_path = Path(self.config[dir_key])
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get_notebook_path(self, architecture: str, pile_type: str) -> Path:
        """
        Get the path to the appropriate notebook.
        
        Parameters:
        -----------
        architecture : str
            Architecture name (dgcnn, pointnet_plus_plus)
        pile_type : str
            Pile type (i_section, c_section)
            
        Returns:
        --------
        notebook_path : Path
            Path to the notebook
        """
        notebooks_dir = Path(self.config['notebooks_dir'])
        notebook_name = f"{pile_type}_pile_detection_{architecture}.ipynb"
        return notebooks_dir / notebook_name
    
    def get_output_notebook_path(self, site: str, ground_method: str, 
                                architecture: str, pile_type: str) -> Path:
        """
        Get the output path for executed notebook.
        
        Parameters:
        -----------
        site : str
            Site name
        ground_method : str
            Ground segmentation method
        architecture : str
            Architecture name
        pile_type : str
            Pile type
            
        Returns:
        --------
        output_path : Path
            Output notebook path
        """
        batch_dir = Path(self.config['batch_output_dir'])
        timestamp = self.start_time.strftime('%Y%m%d_%H%M%S')
        output_name = f"{site}_{ground_method}_{architecture}_{pile_type}_{timestamp}.ipynb"
        return batch_dir / output_name
    
    def prepare_parameters(self, site: str, ground_method: str, 
                          architecture: str, pile_type: str, 
                          confidence_threshold: float) -> Dict[str, Any]:
        """
        Prepare parameters for notebook execution.
        
        Parameters:
        -----------
        site : str
            Site name
        ground_method : str
            Ground segmentation method
        architecture : str
            Architecture name
        pile_type : str
            Pile type
        confidence_threshold : float
            Confidence threshold
            
        Returns:
        --------
        parameters : dict
            Parameters dictionary for Papermill
        """
        model_filename = f"{architecture}_{pile_type}_pile.pth"
        
        parameters = {
            'site_name': site,
            'ground_method': ground_method,
            'confidence_threshold': confidence_threshold,
            'model_path': str(Path(self.config['models_dir']) / model_filename),
            'input_data_dir': self.config['input_data_dir'],
            'output_dir': self.config['output_dir'],
            'mlflow_experiment_name': f"{self.config['mlflow_experiment']}_{architecture}",
            'mlflow_run_name': f"{pile_type}_{site}_{ground_method}",
            'enable_cross_validation': self.config['enable_cross_validation'],
            'cv_folds': self.config['cv_folds'],
            'enable_enhanced_analysis': self.config['enable_enhanced_analysis']
        }
        
        return parameters
    
    def execute_notebook(self, notebook_path: Path, output_path: Path, 
                        parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a single notebook with given parameters.
        
        Parameters:
        -----------
        notebook_path : Path
            Input notebook path
        output_path : Path
            Output notebook path
        parameters : dict
            Parameters for execution
            
        Returns:
        --------
        result : dict
            Execution result
        """
        if not PAPERMILL_AVAILABLE:
            raise RuntimeError("Papermill is required for batch processing")
        
        start_time = time.time()
        
        try:
            print(f"Executing: {notebook_path.name}")
            print(f"Parameters: {parameters['site_name']}, {parameters['ground_method']}")
            
            pm.execute_notebook(
                input_path=str(notebook_path),
                output_path=str(output_path),
                parameters=parameters,
                kernel_name='python3'
            )
            
            execution_time = time.time() - start_time
            
            result = {
                'status': 'success',
                'notebook_path': str(notebook_path),
                'output_path': str(output_path),
                'parameters': parameters,
                'execution_time': execution_time,
                'timestamp': datetime.now().isoformat(),
                'error': None
            }
            
            print(f"✓ Completed in {execution_time:.1f}s")
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            result = {
                'status': 'failed',
                'notebook_path': str(notebook_path),
                'output_path': str(output_path),
                'parameters': parameters,
                'execution_time': execution_time,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
            
            print(f"✗ Failed after {execution_time:.1f}s: {e}")
        
        return result
    
    def run_batch_processing(self) -> List[Dict[str, Any]]:
        """
        Run batch processing for all combinations.
        
        Returns:
        --------
        results : list
            List of execution results
        """
        total_combinations = (len(self.config['sites']) * 
                            len(self.config['ground_methods']) * 
                            len(self.config['architectures']) * 
                            len(self.config['pile_types']) * 
                            len(self.config['confidence_thresholds']))
        
        print(f"Starting batch processing...")
        print(f"Total combinations: {total_combinations}")
        print(f"Timestamp: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        combination_count = 0
        
        for site in self.config['sites']:
            for ground_method in self.config['ground_methods']:
                for architecture in self.config['architectures']:
                    for pile_type in self.config['pile_types']:
                        for confidence_threshold in self.config['confidence_thresholds']:
                            combination_count += 1
                            
                            print(f"\nCombination {combination_count}/{total_combinations}:")
                            print(f"  Site: {site}")
                            print(f"  Ground Method: {ground_method}")
                            print(f"  Architecture: {architecture}")
                            print(f"  Pile Type: {pile_type}")
                            print(f"  Confidence: {confidence_threshold}")
                            
                            # Get notebook and output paths
                            notebook_path = self.get_notebook_path(architecture, pile_type)
                            output_path = self.get_output_notebook_path(
                                site, ground_method, architecture, pile_type
                            )
                            
                            # Check if notebook exists
                            if not notebook_path.exists():
                                print(f"  ✗ Notebook not found: {notebook_path}")
                                continue
                            
                            # Prepare parameters
                            parameters = self.prepare_parameters(
                                site, ground_method, architecture, pile_type, confidence_threshold
                            )
                            
                            # Execute notebook
                            result = self.execute_notebook(notebook_path, output_path, parameters)
                            self.results.append(result)
        
        return self.results

    def save_batch_results(self) -> Path:
        """
        Save batch processing results to file.

        Returns:
        --------
        results_file : Path
            Path to results file
        """
        timestamp = self.start_time.strftime('%Y%m%d_%H%M%S')
        results_file = Path(self.config['batch_output_dir']) / f"batch_results_{timestamp}.json"

        batch_summary = {
            'start_time': self.start_time.isoformat(),
            'end_time': datetime.now().isoformat(),
            'total_combinations': len(self.results),
            'successful_executions': len([r for r in self.results if r['status'] == 'success']),
            'failed_executions': len([r for r in self.results if r['status'] == 'failed']),
            'config': self.config,
            'results': self.results
        }

        with open(results_file, 'w') as f:
            json.dump(batch_summary, f, indent=2)

        print(f"\nBatch results saved to: {results_file}")
        return results_file

    def generate_summary_report(self) -> str:
        """
        Generate a summary report of batch processing.

        Returns:
        --------
        report : str
            Summary report text
        """
        successful = [r for r in self.results if r['status'] == 'success']
        failed = [r for r in self.results if r['status'] == 'failed']

        total_time = sum(r['execution_time'] for r in self.results)
        avg_time = total_time / len(self.results) if self.results else 0

        report = f"""
Batch Pile Detection Processing Summary
{'=' * 50}

Execution Details:
  Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
  End Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
  Total Duration: {(datetime.now() - self.start_time).total_seconds():.1f} seconds

Results:
  Total Combinations: {len(self.results)}
  Successful Executions: {len(successful)}
  Failed Executions: {len(failed)}
  Success Rate: {len(successful) / len(self.results) * 100:.1f}%

Performance:
  Total Execution Time: {total_time:.1f} seconds
  Average Time per Notebook: {avg_time:.1f} seconds

Configuration:
  Sites: {', '.join(self.config['sites'])}
  Ground Methods: {', '.join(self.config['ground_methods'])}
  Architectures: {', '.join(self.config['architectures'])}
  Pile Types: {', '.join(self.config['pile_types'])}
  Confidence Thresholds: {', '.join(map(str, self.config['confidence_thresholds']))}
"""

        if failed:
            report += f"\nFailed Executions:\n"
            for result in failed:
                params = result['parameters']
                report += f"  - {params['site_name']}_{params['ground_method']}: {result['error']}\n"

        report += f"\nNext Steps:\n"
        report += f"  1. Review individual notebook outputs in: {self.config['batch_output_dir']}\n"
        report += f"  2. Run architecture comparison notebook for analysis\n"
        report += f"  3. Check MLflow UI for detailed metrics (if enabled)\n"

        return report

    def run_architecture_comparison(self) -> bool:
        """
        Run the architecture comparison notebook after batch processing.

        Returns:
        --------
        success : bool
            Whether comparison was successful
        """
        if not PAPERMILL_AVAILABLE:
            print("Papermill not available - skipping architecture comparison")
            return False

        comparison_notebook = Path(self.config['notebooks_dir']) / 'pile_detection_architecture_comparison.ipynb'

        if not comparison_notebook.exists():
            print(f"Architecture comparison notebook not found: {comparison_notebook}")
            return False

        timestamp = self.start_time.strftime('%Y%m%d_%H%M%S')
        output_notebook = Path(self.config['batch_output_dir']) / f"architecture_comparison_{timestamp}.ipynb"

        comparison_parameters = {
            'sites': self.config['sites'],
            'ground_methods': self.config['ground_methods'],
            'architectures': self.config['architectures'],
            'pile_types': self.config['pile_types'],
            'results_dir': self.config['output_dir'],
            'comparison_output_dir': str(Path(self.config['batch_output_dir']) / 'comparison_results')
        }

        try:
            print("\nRunning architecture comparison...")
            pm.execute_notebook(
                input_path=str(comparison_notebook),
                output_path=str(output_notebook),
                parameters=comparison_parameters,
                kernel_name='python3'
            )
            print(f"✓ Architecture comparison completed: {output_notebook}")
            return True

        except Exception as e:
            print(f"✗ Architecture comparison failed: {e}")
            return False


def main():
    """Main function for command-line execution."""
    parser = argparse.ArgumentParser(description='Batch Pile Detection Processing')
    parser.add_argument('--config', type=str, help='Configuration file path')
    parser.add_argument('--sites', nargs='+', help='Sites to process')
    parser.add_argument('--ground-methods', nargs='+', help='Ground segmentation methods')
    parser.add_argument('--architectures', nargs='+', help='Architectures to test')
    parser.add_argument('--pile-types', nargs='+', help='Pile types to detect')
    parser.add_argument('--confidence-thresholds', nargs='+', type=float, help='Confidence thresholds')
    parser.add_argument('--no-comparison', action='store_true', help='Skip architecture comparison')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be executed without running')

    args = parser.parse_args()

    # Initialize processor
    processor = BatchPileDetectionProcessor(args.config)

    # Override config with command line arguments
    if args.sites:
        processor.config['sites'] = args.sites
    if args.ground_methods:
        processor.config['ground_methods'] = args.ground_methods
    if args.architectures:
        processor.config['architectures'] = args.architectures
    if args.pile_types:
        processor.config['pile_types'] = args.pile_types
    if args.confidence_thresholds:
        processor.config['confidence_thresholds'] = args.confidence_thresholds

    if args.dry_run:
        # Show what would be executed
        total_combinations = (len(processor.config['sites']) *
                            len(processor.config['ground_methods']) *
                            len(processor.config['architectures']) *
                            len(processor.config['pile_types']) *
                            len(processor.config['confidence_thresholds']))

        print("Dry Run - Would execute the following combinations:")
        print(f"Total combinations: {total_combinations}")
        print(f"Sites: {processor.config['sites']}")
        print(f"Ground methods: {processor.config['ground_methods']}")
        print(f"Architectures: {processor.config['architectures']}")
        print(f"Pile types: {processor.config['pile_types']}")
        print(f"Confidence thresholds: {processor.config['confidence_thresholds']}")
        return

    try:
        # Run batch processing
        results = processor.run_batch_processing()

        # Save results
        results_file = processor.save_batch_results()

        # Generate and print summary
        summary = processor.generate_summary_report()
        print(summary)

        # Save summary to file
        summary_file = Path(processor.config['batch_output_dir']) / f"summary_{processor.start_time.strftime('%Y%m%d_%H%M%S')}.txt"
        with open(summary_file, 'w') as f:
            f.write(summary)
        print(f"Summary saved to: {summary_file}")

        # Run architecture comparison if requested
        if not args.no_comparison:
            processor.run_architecture_comparison()

        print(f"\nBatch processing completed successfully!")
        print(f"Results available in: {processor.config['batch_output_dir']}")

    except KeyboardInterrupt:
        print("\nBatch processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nBatch processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
