#!/bin/bash
set -e

# ---------- Configurable Parameters ----------
DIR="$HOME/Code/throwaway/pytorch-geometric-developer-install"
PYTHON_VERSION=3.11
TORCH_VERSION=2.2.0
CUDA="cpu"
ENV_PATH="$DIR/.venv"
KERNEL_NAME="pyg_dev"
GITHUB_USERNAME="project-delphi"
MIN_MACOSX_DEPLOYMENT_TARGET=$(sw_vers -productVersion)
PYTHON_PATH_ENV="$ENV_PATH/bin/python"

# ---------- Conda Setup ----------
if ! command -v conda &>/dev/null; then
  echo "Installing Miniconda (Apple Silicon)..."
  curl -O https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-arm64.sh
  bash Miniconda3-latest-MacOSX-arm64.sh -b -u
  rm Miniconda3-latest-MacOSX-arm64.sh
  eval "$(~/miniconda3/bin/conda shell.bash hook)"
fi

echo "Creating/activating Conda environment at $ENV_PATH..."
mkdir -p "$DIR"; cd "$DIR"
eval "$(conda shell.bash hook)"
conda create --yes -p "$ENV_PATH" python="$PYTHON_VERSION" || true
conda activate "$ENV_PATH"

# ---------- Install Essential Packages ----------
echo "Installing core and build tools..."
conda install --yes pip ipykernel cmake ninja setuptools pybind11 -c conda-forge

echo "Installing PyTorch (CPU/ARM64) and related packages..."
conda install --yes pytorch torchvision torchaudio cpuonly -c pytorch -c conda-forge

# ---------- Register Jupyter Kernel ----------
echo "Registering Jupyter kernel '$KERNEL_NAME'..."
"$PYTHON_PATH_ENV" -m ipykernel install --user \
  --name "$KERNEL_NAME" \
  --display-name "Python (pyg_dev)"

# ---------- PyTorch Geometric Extensions ----------
PYG_URL="https://data.pyg.org/whl/torch-${TORCH_VERSION}+${CUDA}.html"
echo "Installing PyG extension dependencies..."
MACOSX_DEPLOYMENT_TARGET=$MIN_MACOSX_DEPLOYMENT_TARGET CC=clang CXX=clang++ \
pip install --no-cache-dir \
  numpy==1.26.4 mpmath==1.3.0 \
  torch-scatter torch-sparse torch-cluster torch-spline-conv \
  -f "$PYG_URL"

echo "Installing pyg-lib..."
if ! MACOSX_DEPLOYMENT_TARGET=$MIN_MACOSX_DEPLOYMENT_TARGET CC=clang CXX=clang++ \
     pip install --no-cache-dir pyg-lib -f "$PYG_URL"; then
  echo "Fallback to build pyg-lib from source"
  pip install --no-cache-dir --no-build-isolation git+https://github.com/pyg-team/pyg-lib.git
fi

# ---------- ONNX & Protobuf ----------
echo "Installing ONNX + compatibility fixes..."
pip install jsonschema==4.19.0 protobuf==4.25.3 onnx==1.16.0 onnxscript==0.1.0

# ---------- Clone & Install PyTorch Geometric ----------
echo "Cloning or reusing PyTorch Geometric repo..."
if [ ! -d "pytorch_geometric/.git" ]; then
  git clone "https://github.com/$GITHUB_USERNAME/pytorch_geometric.git"
  cd pytorch_geometric
else
  cd pytorch_geometric
  echo "Repository exists — fetching upstream updates..."
  git fetch upstream || git remote add upstream https://github.com/pyg-team/pytorch_geometric
  git rebase upstream/master
fi

echo "Installing PyTorch Geometric in editable mode..."
MACOSX_DEPLOYMENT_TARGET=$MIN_MACOSX_DEPLOYMENT_TARGET CC=clang CXX=clang++ \
pip install --no-cache-dir -e ".[dev,full]"

# ---------- Final Verification ----------
echo "✅ Installation complete. Verifying…"
python --version
python - <<EOF
import torch, torch_geometric
print("torch:", torch.__version__, "| PyG:", torch_geometric.__version__)
EOF
