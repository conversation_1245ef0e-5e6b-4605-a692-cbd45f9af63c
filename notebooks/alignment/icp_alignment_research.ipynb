{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICP-Based Point Cloud Alignment\n", "\n", "This notebook implements traditional Iterative Closest Point (ICP) alignment for point cloud registration. It provides a comprehensive implementation with modular execution cells for clarity and detailed analysis of ICP performance.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Source and target point clouds  \n", "**Output**: Aligned point cloud with transformation matrix  \n", "**Method**: Traditional ICP with multiple variants  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Environment Setup**: Import libraries and configure parameters\n", "2. **Data Loading**: Load source and target point clouds\n", "3. **Preprocessing**: Normalize and downsample point clouds\n", "4. **ICP Implementation**: Multiple ICP variants and optimizations\n", "5. **Evaluation**: Performance metrics and quality assessment\n", "6. **Visualization**: Comprehensive alignment results\n", "7. **Export**: Save aligned point clouds and metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment with required libraries and parameters for ICP alignment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install open3d matplotlib laspy transforms3d scipy pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import logging\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "import json\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"ICP Alignment Environment Initialized\")\n", "print(f\"Open3D version: {o3d.__version__}\")\n", "print(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Parameters\n", "\n", "Define ICP algorithm parameters and data paths."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ICPConfig:\n", "    \"\"\"Configuration parameters for ICP alignment.\"\"\"\n", "    \n", "    # Data paths\n", "    PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "    PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "    \n", "    # ICP Parameters\n", "    MAX_ITERATIONS = 50\n", "    TOLERANCE = 1e-6\n", "    VOXEL_SIZE = 0.02  # For downsampling\n", "    \n", "    # Evaluation parameters\n", "    DISTANCE_THRESHOLD = 0.1  # For correspondence evaluation\n", "    \n", "    def __init__(self):\n", "        self.base_path = Path('../..')\n", "        self.data_path = self.base_path / 'data'\n", "        self.input_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'preprocessing'\n", "        self.output_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'alignment'\n", "        self.output_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        print(f\"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}\")\n", "        print(f\"Input path: {self.input_path}\")\n", "        print(f\"Output path: {self.output_path}\")\n", "\n", "config = ICPConfig()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Loading Functions\n", "\n", "Implement functions to load point clouds from various formats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_las_file(filename, num_points=None):\n", "    \"\"\"\n", "    Reads a LAS file and returns the points as a numpy array.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the LAS file\n", "    num_points : int, optional\n", "        Number of points to read (if None, reads all points)\n", "    \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    \"\"\"\n", "    logger.info(f\"Reading LAS file: {filename}\")\n", "    try:\n", "        las_data = laspy.read(filename)\n", "        \n", "        # Determine the number of points to read\n", "        if num_points is None:\n", "            num_points = len(las_data.x)\n", "        else:\n", "            num_points = min(num_points, len(las_data.x))\n", "        \n", "        # Extract XYZ coordinates\n", "        x = np.array(las_data.x[:num_points], dtype=np.float64)\n", "        y = np.array(las_data.y[:num_points], dtype=np.float64)\n", "        z = np.array(las_data.z[:num_points], dtype=np.float64)\n", "        \n", "        # Stack XYZ coordinates\n", "        points = np.column_stack((x, y, z))\n", "        logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "        return points\n", "    except Exception as e:\n", "        logger.error(f\"Error reading LAS file '{filename}': {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_point_cloud_file(filename):\n", "    \"\"\"\n", "    Reads point cloud from various formats (PLY, PCD, OBJ).\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the point cloud file\n", "    \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    \"\"\"\n", "    logger.info(f\"Reading point cloud file: {filename}\")\n", "    try:\n", "        file_path = Path(filename)\n", "        \n", "        if file_path.suffix.lower() == '.las':\n", "            return read_las_file(filename)\n", "        elif file_path.suffix.lower() in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(filename))\n", "            points = np.asarray(pcd.points)\n", "            logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.obj':\n", "            mesh = o3d.io.read_triangle_mesh(str(filename))\n", "            points = np.asarray(mesh.vertices)\n", "            logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "            return points\n", "        else:\n", "            logger.error(f\"Unsupported file format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error reading point cloud file '{filename}': {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Point Cloud Preprocessing\n", "\n", "Implement preprocessing functions for normalization and downsampling."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud of shape (N, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    normalized : numpy.n<PERSON>ray\n", "        Normalized point cloud of shape (N, 3)\n", "    centroid : numpy.n<PERSON><PERSON>\n", "        Original centroid for denormalization\n", "    scale : float\n", "        Original scale for denormalization\n", "    \"\"\"\n", "    # Center the point cloud at the origin\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale the point cloud to fit inside a unit sphere\n", "    furthest_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if furthest_distance > 0:\n", "        normalized = centered / furthest_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized, centroid, furthest_distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def downsample_voxel(points, voxel_size=0.02):\n", "    \"\"\"\n", "    Applies voxel grid downsampling to reduce point count while preserving structure.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud of shape (N, 3)\n", "    voxel_size : float\n", "        Size of voxel grid cells for downsampling\n", "    \n", "    Returns:\n", "    --------\n", "    downsampled : numpy.n<PERSON><PERSON>\n", "        Downsampled point cloud\n", "    \"\"\"\n", "    # Convert numpy array to Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Apply voxel downsampling\n", "    downsampled = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "    \n", "    # Convert back to numpy array\n", "    return np.asarray(downsampled.points)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. ICP Algorithm Implementation\n", "\n", "Implement the core ICP algorithm with multiple variants."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def nearest_neighbor(source, target):\n", "    \"\"\"\n", "    Find nearest neighbors between source and target point clouds.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    distances : numpy.ndarray\n", "        Distances to nearest neighbors\n", "    indices : numpy.ndarray\n", "        Indices of nearest neighbors in target\n", "    \"\"\"\n", "    tree = cKDTree(target)\n", "    distances, indices = tree.query(source)\n", "    return distances, indices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def best_fit_transform(source, target):\n", "    \"\"\"\n", "    Calculates the least-squares best-fit transform between corresponding 3D points.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source points of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target points of shape (N, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    T : numpy.n<PERSON><PERSON>\n", "        Homogeneous transformation matrix (4, 4)\n", "    R : numpy.n<PERSON><PERSON>\n", "        Rotation matrix (3, 3)\n", "    t : numpy.n<PERSON><PERSON>\n", "        Translation vector (3,)\n", "    \"\"\"\n", "    assert source.shape == target.shape, \"Source and target must have the same shape\"\n", "    \n", "    # Center both point clouds\n", "    source_centroid = np.mean(source, axis=0)\n", "    target_centroid = np.mean(target, axis=0)\n", "    source_centered = source - source_centroid\n", "    target_centered = target - target_centroid\n", "    \n", "    # Compute covariance matrix H\n", "    H = np.dot(source_centered.T, target_centered)\n", "    \n", "    # Singular Value Decomposition\n", "    U, S, Vt = np.linalg.svd(H)\n", "    \n", "    # Compute rotation matrix R\n", "    R = np.dot(Vt.T, U.T)\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = np.dot(Vt.T, U.T)\n", "    \n", "    # Compute translation\n", "    t = target_centroid - np.dot(R, source_centroid)\n", "    \n", "    # Create homogeneous transformation matrix\n", "    T = np.identity(4)\n", "    T[:3, :3] = R\n", "    T[:3, 3] = t\n", "    \n", "    return T, R, t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def icp_algorithm(source, target, max_iterations=50, tolerance=1e-6, verbose=False):\n", "    \"\"\"\n", "    Iterative Closest Point (ICP) algorithm for point cloud alignment.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    max_iterations : int\n", "        Maximum number of iterations\n", "    tolerance : float\n", "        Convergence tolerance\n", "    verbose : bool\n", "        Whether to print progress information\n", "    \n", "    Returns:\n", "    --------\n", "    T_combined : numpy.n<PERSON><PERSON>\n", "        Final transformation matrix (4, 4)\n", "    aligned_source : numpy.ndarray\n", "        Aligned source point cloud\n", "    final_error : float\n", "        Final mean squared error\n", "    iterations : int\n", "        Number of iterations performed\n", "    convergence_history : list\n", "        History of error values for each iteration\n", "    \"\"\"\n", "    # Make a copy of the source point cloud\n", "    source_copy = np.copy(source)\n", "    prev_error = float('inf')\n", "    convergence_history = []\n", "    \n", "    # Initialize transformation matrix\n", "    T_combined = np.identity(4)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for iteration in range(max_iterations):\n", "        # Find nearest neighbors\n", "        distances, indices = nearest_neighbor(source_copy, target)\n", "        \n", "        # Compute mean squared error\n", "        mean_error = np.mean(distances**2)\n", "        convergence_history.append(mean_error)\n", "        \n", "        # Check for convergence\n", "        if verbose:\n", "            print(f\"Iteration {iteration+1:3d}, MSE: {mean_error:.10f}\")\n", "        \n", "        if abs(prev_error - mean_error) < tolerance:\n", "            if verbose:\n", "                print(f\"Converged after {iteration+1} iterations.\")\n", "            break\n", "        \n", "        prev_error = mean_error\n", "        \n", "        # Get corresponding points\n", "        corresponding_target_points = target[indices]\n", "        \n", "        # Compute transformation\n", "        T, R, t = best_fit_transform(source_copy, corresponding_target_points)\n", "        \n", "        # Update transformation matrix\n", "        T_combined = np.dot(T, T_combined)\n", "        \n", "        # Apply transformation\n", "        source_copy = np.dot(source_copy, R.T) + t\n", "    \n", "    end_time = time.time()\n", "    \n", "    if verbose:\n", "        print(f\"ICP completed in {end_time - start_time:.4f} seconds\")\n", "        if iteration == max_iterations - 1:\n", "            print(f\"Warning: Maximum iterations ({max_iterations}) reached without convergence.\")\n", "    \n", "    return T_combined, source_copy, mean_error, iteration + 1, convergence_history"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}