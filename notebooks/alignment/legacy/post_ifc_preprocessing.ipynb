{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Post-IFC Preprocessing\n", "\n", "This notebook handles preprocessing steps after IFC to point cloud conversion and metadata extraction, preparing the data for alignment.\n", "\n", "**Steps covered:**\n", "1. Point cloud quality assessment\n", "2. Initial cleaning and filtering\n", "3. Coordinate system validation\n", "4. Downsampling and optimization\n", "5. Preparation for alignment workflow\n", "\n", "**Author:** <PERSON><PERSON><PERSON>  \n", "**Date:** December 2024"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install open3d matplotlib numpy scipy pandas laspy rasterio"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import open3d as o3d\n", "import pandas as pd\n", "from scipy.spatial import cKDTree\n", "from scipy import stats\n", "import json\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"Open3D version: {o3d.__version__}\")\n", "print(f\"NumPy version: {np.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Point Cloud Quality Assessment\n", "\n", "Assess the quality of the converted IFC point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def assess_point_cloud_quality(points, metadata=None):\n", "    \"\"\"\n", "    Assess the quality of a point cloud.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    metadata : dict, optional\n", "        Additional metadata from IFC conversion\n", "        \n", "    Returns:\n", "    --------\n", "    quality_report : dict\n", "        Quality assessment report\n", "    \"\"\"\n", "    if points is None or len(points) == 0:\n", "        return {'status': 'error', 'message': 'No points provided'}\n", "    \n", "    # Basic statistics\n", "    num_points = len(points)\n", "    bounds = {\n", "        'min': points.min(axis=0),\n", "        'max': points.max(axis=0),\n", "        'range': points.max(axis=0) - points.min(axis=0)\n", "    }\n", "    \n", "    # Point density analysis\n", "    volume = np.prod(bounds['range'])\n", "    density = num_points / volume if volume > 0 else 0\n", "    \n", "    # Detect outliers using statistical methods\n", "    z_scores = np.abs(stats.zscore(points, axis=0))\n", "    outliers = np.any(z_scores > 3, axis=1)\n", "    outlier_percentage = np.sum(outliers) / num_points * 100\n", "    \n", "    # Check for duplicate points\n", "    unique_points = np.unique(points, axis=0)\n", "    duplicate_percentage = (num_points - len(unique_points)) / num_points * 100\n", "    \n", "    # Nearest neighbor analysis\n", "    if num_points > 1:\n", "        tree = cKDTree(points)\n", "        distances, _ = tree.query(points, k=2)  # k=2 to exclude self\n", "        nn_distances = distances[:, 1]  # Nearest neighbor distances\n", "        \n", "        nn_stats = {\n", "            'mean': np.mean(nn_distances),\n", "            'std': np.std(nn_distances),\n", "            'min': np.min(nn_distances),\n", "            'max': np.max(nn_distances),\n", "            'median': np.median(nn_distances)\n", "        }\n", "    else:\n", "        nn_stats = None\n", "    \n", "    # Quality assessment\n", "    quality_issues = []\n", "    \n", "    if num_points < 100:\n", "        quality_issues.append(\"Very low point count\")\n", "    \n", "    if outlier_percentage > 5:\n", "        quality_issues.append(f\"High outlier percentage: {outlier_percentage:.1f}%\")\n", "    \n", "    if duplicate_percentage > 1:\n", "        quality_issues.append(f\"High duplicate percentage: {duplicate_percentage:.1f}%\")\n", "    \n", "    if nn_stats and nn_stats['std'] / nn_stats['mean'] > 2:\n", "        quality_issues.append(\"Highly irregular point spacing\")\n", "    \n", "    # Overall quality score (0-100)\n", "    quality_score = 100\n", "    quality_score -= min(outlier_percentage * 2, 30)  # Penalize outliers\n", "    quality_score -= min(duplicate_percentage * 5, 20)  # Penalize duplicates\n", "    if num_points < 1000:\n", "        quality_score -= (1000 - num_points) / 1000 * 20  # Penalize low count\n", "    \n", "    quality_report = {\n", "        'status': 'success',\n", "        'num_points': num_points,\n", "        'bounds': bounds,\n", "        'density': density,\n", "        'outlier_percentage': outlier_percentage,\n", "        'duplicate_percentage': duplicate_percentage,\n", "        'nearest_neighbor_stats': nn_stats,\n", "        'quality_score': max(0, quality_score),\n", "        'quality_issues': quality_issues,\n", "        'metadata': metadata\n", "    }\n", "    \n", "    return quality_report\n", "\n", "def print_quality_report(quality_report):\n", "    \"\"\"\n", "    Print a formatted quality assessment report.\n", "    \"\"\"\n", "    if quality_report['status'] != 'success':\n", "        print(f\"Error: {quality_report['message']}\")\n", "        return\n", "    \n", "    print(\"=== Point Cloud Quality Assessment ===\")\n", "    print(f\"Number of points: {quality_report['num_points']:,}\")\n", "    print(f\"Bounding box: {quality_report['bounds']['range']}\")\n", "    print(f\"Point density: {quality_report['density']:.2f} points/unit³\")\n", "    print(f\"Outlier percentage: {quality_report['outlier_percentage']:.2f}%\")\n", "    print(f\"Duplicate percentage: {quality_report['duplicate_percentage']:.2f}%\")\n", "    print(f\"Quality score: {quality_report['quality_score']:.1f}/100\")\n", "    \n", "    if quality_report['nearest_neighbor_stats']:\n", "        nn = quality_report['nearest_neighbor_stats']\n", "        print(f\"Nearest neighbor distance: {nn['mean']:.4f} ± {nn['std']:.4f}\")\n", "    \n", "    if quality_report['quality_issues']:\n", "        print(\"\\nQuality Issues:\")\n", "        for issue in quality_report['quality_issues']:\n", "            print(f\"  - {issue}\")\n", "    else:\n", "        print(\"\\nNo significant quality issues detected.\")\n", "    \n", "    print(\"=\" * 40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Cleaning and Filtering\n", "\n", "Clean and filter the point cloud based on quality assessment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_point_cloud(points, remove_outliers=True, remove_duplicates=True, \n", "                     outlier_std_ratio=2.0, outlier_nb_neighbors=20):\n", "    \"\"\"\n", "    Clean point cloud by removing outliers and duplicates.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud (N, 3)\n", "    remove_outliers : bool\n", "        Whether to remove statistical outliers\n", "    remove_duplicates : bool\n", "        Whether to remove duplicate points\n", "    outlier_std_ratio : float\n", "        Standard deviation ratio for outlier detection\n", "    outlier_nb_neighbors : int\n", "        Number of neighbors for outlier detection\n", "        \n", "    Returns:\n", "    --------\n", "    cleaned_points : numpy.n<PERSON><PERSON>\n", "        Cleaned point cloud\n", "    cleaning_stats : dict\n", "        Statistics about the cleaning process\n", "    \"\"\"\n", "    original_count = len(points)\n", "    current_points = points.copy()\n", "    \n", "    cleaning_stats = {\n", "        'original_count': original_count,\n", "        'duplicates_removed': 0,\n", "        'outliers_removed': 0,\n", "        'final_count': 0\n", "    }\n", "    \n", "    # Remove duplicates\n", "    if remove_duplicates:\n", "        unique_points, unique_indices = np.unique(current_points, axis=0, return_index=True)\n", "        duplicates_removed = len(current_points) - len(unique_points)\n", "        current_points = unique_points\n", "        cleaning_stats['duplicates_removed'] = duplicates_removed\n", "        print(f\"Removed {duplicates_removed} duplicate points\")\n", "    \n", "    # Remove statistical outliers using Open3D\n", "    if remove_outliers and len(current_points) > outlier_nb_neighbors:\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(current_points)\n", "        \n", "        # Statistical outlier removal\n", "        pcd_clean, outlier_indices = pcd.remove_statistical_outlier(\n", "            nb_neighbors=outlier_nb_neighbors,\n", "            std_ratio=outlier_std_ratio\n", "        )\n", "        \n", "        outliers_removed = len(current_points) - len(pcd_clean.points)\n", "        current_points = np.asarray(pcd_clean.points)\n", "        cleaning_stats['outliers_removed'] = outliers_removed\n", "        print(f\"Removed {outliers_removed} outlier points\")\n", "    \n", "    cleaning_stats['final_count'] = len(current_points)\n", "    \n", "    print(f\"Cleaning complete: {original_count} → {len(current_points)} points\")\n", "    print(f\"Reduction: {(1 - len(current_points)/original_count)*100:.1f}%\")\n", "    \n", "    return current_points, cleaning_stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Coordinate System Validation\n", "\n", "Validate and normalize coordinate systems."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def validate_coordinate_system(points, expected_crs=None, metadata=None):\n", "    \"\"\"\n", "    Validate coordinate system and detect potential issues.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    expected_crs : str, optional\n", "        Expected coordinate reference system\n", "    metadata : dict, optional\n", "        Metadata from IFC conversion\n", "        \n", "    Returns:\n", "    --------\n", "    validation_report : dict\n", "        Coordinate system validation report\n", "    \"\"\"\n", "    bounds = {\n", "        'min': points.min(axis=0),\n", "        'max': points.max(axis=0),\n", "        'range': points.max(axis=0) - points.min(axis=0),\n", "        'center': points.mean(axis=0)\n", "    }\n", "    \n", "    issues = []\n", "    recommendations = []\n", "    \n", "    # Check for very large coordinates (might indicate wrong CRS)\n", "    if np.any(np.abs(bounds['center']) > 1e6):\n", "        issues.append(\"Very large coordinate values detected\")\n", "        recommendations.append(\"Consider coordinate system transformation\")\n", "    \n", "    # Check for very small ranges (might indicate unit issues)\n", "    if np.any(bounds['range'] < 1e-3):\n", "        issues.append(\"Very small coordinate ranges detected\")\n", "        recommendations.append(\"Check if units are correct (mm vs m)\")\n", "    \n", "    # Check for very large ranges\n", "    if np.any(bounds['range'] > 1e6):\n", "        issues.append(\"Very large coordinate ranges detected\")\n", "        recommendations.append(\"Verify coordinate system and data extent\")\n", "    \n", "    # Check Z-coordinate reasonableness\n", "    z_range = bounds['range'][2]\n", "    z_min = bounds['min'][2]\n", "    \n", "    if z_min < -1000:\n", "        issues.append(\"Very low Z coordinates (below -1000)\")\n", "        recommendations.append(\"Check elevation reference system\")\n", "    \n", "    if z_range > 10000:\n", "        issues.append(\"Very large Z range (>10km)\")\n", "        recommendations.append(\"Verify elevation data validity\")\n", "    \n", "    # Aspect ratio analysis\n", "    xy_range = bounds['range'][:2]\n", "    aspect_ratio = np.max(xy_range) / np.min(xy_range) if np.min(xy_range) > 0 else np.inf\n", "    \n", "    if aspect_ratio > 100:\n", "        issues.append(f\"Very high aspect ratio: {aspect_ratio:.1f}\")\n", "        recommendations.append(\"Data might be elongated or have coordinate issues\")\n", "    \n", "    validation_report = {\n", "        'bounds': bounds,\n", "        'aspect_ratio': aspect_ratio,\n", "        'issues': issues,\n", "        'recommendations': recommendations,\n", "        'expected_crs': expected_crs,\n", "        'metadata_crs': metadata.get('crs') if metadata else None\n", "    }\n", "    \n", "    return validation_report\n", "\n", "def normalize_coordinates(points, method='center'):\n", "    \"\"\"\n", "    Normalize coordinates for better numerical stability.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud (N, 3)\n", "    method : str\n", "        Normalization method ('center', 'unit_sphere', 'unit_cube')\n", "        \n", "    Returns:\n", "    --------\n", "    normalized_points : numpy.n<PERSON><PERSON>\n", "        Normalized point cloud\n", "    transform_info : dict\n", "        Information to reverse the transformation\n", "    \"\"\"\n", "    original_center = points.mean(axis=0)\n", "    centered_points = points - original_center\n", "    \n", "    if method == 'center':\n", "        normalized_points = centered_points\n", "        scale_factor = 1.0\n", "        \n", "    elif method == 'unit_sphere':\n", "        max_distance = np.max(np.linalg.norm(centered_points, axis=1))\n", "        scale_factor = 1.0 / max_distance if max_distance > 0 else 1.0\n", "        normalized_points = centered_points * scale_factor\n", "        \n", "    elif method == 'unit_cube':\n", "        max_range = np.max(np.abs(centered_points))\n", "        scale_factor = 1.0 / max_range if max_range > 0 else 1.0\n", "        normalized_points = centered_points * scale_factor\n", "        \n", "    else:\n", "        raise ValueError(f\"Unknown normalization method: {method}\")\n", "    \n", "    transform_info = {\n", "        'method': method,\n", "        'original_center': original_center,\n", "        'scale_factor': scale_factor\n", "    }\n", "    \n", "    return normalized_points, transform_info"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Downsampling and Optimization\n", "\n", "Optimize point cloud for alignment processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def optimize_for_alignment(points, target_points=None, max_points=50000, \n", "                          voxel_size='auto', preserve_features=True):\n", "    \"\"\"\n", "    Optimize point cloud for alignment processing.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud (N, 3)\n", "    target_points : int, optional\n", "        Target number of points\n", "    max_points : int\n", "        Maximum number of points to keep\n", "    voxel_size : float or 'auto'\n", "        Voxel size for downsampling\n", "    preserve_features : bool\n", "        Whether to preserve geometric features\n", "        \n", "    Returns:\n", "    --------\n", "    optimized_points : numpy.ndarray\n", "        Optimized point cloud\n", "    optimization_stats : dict\n", "        Statistics about the optimization\n", "    \"\"\"\n", "    original_count = len(points)\n", "    \n", "    # Determine target point count\n", "    if target_points is None:\n", "        target_points = min(max_points, original_count)\n", "    \n", "    # Auto-determine voxel size if needed\n", "    if voxel_size == 'auto':\n", "        # Calculate voxel size based on point cloud bounds and target points\n", "        bounds_range = points.max(axis=0) - points.min(axis=0)\n", "        volume = np.prod(bounds_range)\n", "        \n", "        # Estimate voxel size for target density\n", "        target_density = target_points / volume\n", "        voxel_size = (1.0 / target_density) ** (1/3)\n", "        \n", "        # Ensure reasonable voxel size\n", "        min_voxel = np.min(bounds_range) / 1000  # At least 1000 voxels per dimension\n", "        max_voxel = np.min(bounds_range) / 10    # At most 10 voxels per dimension\n", "        voxel_size = np.clip(voxel_size, min_voxel, max_voxel)\n", "    \n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Voxel downsampling\n", "    pcd_downsampled = pcd.voxel_down_sample(voxel_size)\n", "    downsampled_points = np.asarray(pcd_downsampled.points)\n", "    \n", "    # If still too many points, use random sampling\n", "    if len(downsampled_points) > max_points:\n", "        indices = np.random.choice(len(downsampled_points), max_points, replace=False)\n", "        downsampled_points = downsampled_points[indices]\n", "    \n", "    optimization_stats = {\n", "        'original_count': original_count,\n", "        'final_count': len(downsampled_points),\n", "        'reduction_ratio': len(downsampled_points) / original_count,\n", "        'voxel_size': voxel_size,\n", "        'target_points': target_points\n", "    }\n", "    \n", "    print(f\"Optimization complete: {original_count} → {len(downsampled_points)} points\")\n", "    print(f\"Reduction ratio: {optimization_stats['reduction_ratio']:.3f}\")\n", "    print(f\"Voxel size used: {voxel_size:.6f}\")\n", "    \n", "    return downsampled_points, optimization_stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Metadata Integration and Usage\n", "\n", "Load and integrate metadata from IFC/CAD extraction notebooks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_extracted_metadata(metadata_path, metadata_type='ifc'):\n", "    \"\"\"\n", "    Load metadata extracted from IFC or CAD files.\n", "    \n", "    Parameters:\n", "    -----------\n", "    metadata_path : str\n", "        Path to the metadata CSV file\n", "    metadata_type : str\n", "        Type of metadata ('ifc' or 'cad')\n", "        \n", "    Returns:\n", "    --------\n", "    metadata_df : pandas.DataFrame\n", "        Loaded metadata\n", "    metadata_summary : dict\n", "        Summary of the metadata\n", "    \"\"\"\n", "    try:\n", "        metadata_df = pd.read_csv(metadata_path)\n", "        \n", "        # Create summary based on metadata type\n", "        if metadata_type == 'ifc':\n", "            # IFC metadata typically has: Pile No., Tracker No., x, y, z, Longitude, Latitude\n", "            summary = {\n", "                'total_elements': len(metadata_df),\n", "                'coordinate_system': 'IFC Local + Geographic',\n", "                'has_geographic_coords': 'Longitude' in metadata_df.columns and 'Latitude' in metadata_df.columns,\n", "                'element_types': metadata_df.get('Pile Type', pd.Series()).value_counts().to_dict(),\n", "                'spatial_bounds': {\n", "                    'x_range': [metadata_df['x'].min(), metadata_df['x'].max()] if 'x' in metadata_df.columns else None,\n", "                    'y_range': [metadata_df['y'].min(), metadata_df['y'].max()] if 'y' in metadata_df.columns else None,\n", "                    'z_range': [metadata_df['z'].min(), metadata_df['z'].max()] if 'z' in metadata_df.columns else None\n", "                }\n", "            }\n", "        elif metadata_type == 'cad':\n", "            # CAD metadata typically has: <PERSON><PERSON>, Pile No., X_Local, Y_Local, Z_Local, Block Name\n", "            summary = {\n", "                'total_elements': len(metadata_df),\n", "                'coordinate_system': 'CAD Local',\n", "                'has_geographic_coords': 'Longitude' in metadata_df.columns and 'Latitude' in metadata_df.columns,\n", "                'block_types': metadata_df.get('Block Name', pd.Series()).value_counts().to_dict(),\n", "                'layers': metadata_df.get('Layer', pd.Series()).value_counts().to_dict(),\n", "                'spatial_bounds': {\n", "                    'x_range': [metadata_df['X_Local'].min(), metadata_df['X_Local'].max()] if 'X_Local' in metadata_df.columns else None,\n", "                    'y_range': [metadata_df['Y_Local'].min(), metadata_df['Y_Local'].max()] if 'Y_Local' in metadata_df.columns else None,\n", "                    'z_range': [metadata_df['Z_Local'].min(), metadata_df['Z_Local'].max()] if 'Z_Local' in metadata_df.columns else None\n", "                }\n", "            }\n", "        \n", "        print(f\"Loaded {metadata_type.upper()} metadata: {len(metadata_df)} elements\")\n", "        print(f\"Columns: {list(metadata_df.columns)}\")\n", "        \n", "        return metadata_df, summary\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading metadata from {metadata_path}: {e}\")\n", "        return None, None\n", "\n", "def integrate_metadata_with_pointcloud(points, metadata_df, metadata_type='ifc', \n", "                                     spatial_tolerance=1.0):\n", "    \"\"\"\n", "    Integrate metadata with point cloud by spatial matching.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    metadata_df : pandas.DataFrame\n", "        Metadata from IFC/CAD extraction\n", "    metadata_type : str\n", "        Type of metadata ('ifc' or 'cad')\n", "    spatial_tolerance : float\n", "        Tolerance for spatial matching in meters\n", "        \n", "    Returns:\n", "    --------\n", "    integrated_data : dict\n", "        Dictionary containing integrated information\n", "    \"\"\"\n", "    if metadata_df is None:\n", "        return None\n", "    \n", "    # Extract coordinates from metadata based on type\n", "    if metadata_type == 'ifc':\n", "        if all(col in metadata_df.columns for col in ['x', 'y', 'z']):\n", "            metadata_coords = metadata_df[['x', 'y', 'z']].values\n", "        else:\n", "            print(\"IFC metadata missing coordinate columns (x, y, z)\")\n", "            return None\n", "    elif metadata_type == 'cad':\n", "        if all(col in metadata_df.columns for col in ['X_Local', 'Y_Local', 'Z_Local']):\n", "            metadata_coords = metadata_df[['X_Local', 'Y_Local', 'Z_Local']].values\n", "        else:\n", "            print(\"CAD metadata missing coordinate columns (X_Local, Y_Local, Z_Local)\")\n", "            return None\n", "    \n", "    # Find spatial matches between point cloud and metadata\n", "    from scipy.spatial import cKDTree\n", "    \n", "    # Build KD-tree for metadata coordinates\n", "    metadata_tree = cKDTree(metadata_coords)\n", "    \n", "    # Find nearest metadata points for each point cloud point\n", "    distances, indices = metadata_tree.query(points, distance_upper_bound=spatial_tolerance)\n", "    \n", "    # Count matches\n", "    valid_matches = distances < spatial_tolerance\n", "    num_matches = np.sum(valid_matches)\n", "    \n", "    integrated_data = {\n", "        'metadata_type': metadata_type,\n", "        'total_metadata_elements': len(metadata_df),\n", "        'total_point_cloud_points': len(points),\n", "        'spatial_matches': num_matches,\n", "        'match_percentage': (num_matches / len(points)) * 100,\n", "        'spatial_tolerance': spatial_tolerance,\n", "        'matched_indices': indices[valid_matches],\n", "        'matched_metadata': metadata_df.iloc[indices[valid_matches]].copy() if num_matches > 0 else None\n", "    }\n", "    \n", "    print(f\"Spatial integration results:\")\n", "    print(f\"- Metadata elements: {len(metadata_df)}\")\n", "    print(f\"- Point cloud points: {len(points)}\")\n", "    print(f\"- Spatial matches: {num_matches} ({integrated_data['match_percentage']:.1f}%)\")\n", "    print(f\"- Tolerance used: {spatial_tolerance}m\")\n", "    \n", "    return integrated_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Complete Workflow Integration\n", "\n", "Demonstrate complete workflow with metadata integration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def complete_post_ifc_workflow(point_cloud_path, metadata_path=None, metadata_type='ifc'):\n", "    \"\"\"\n", "    Complete post-IFC preprocessing workflow with metadata integration.\n", "    \n", "    Parameters:\n", "    -----------\n", "    point_cloud_path : str\n", "        Path to the point cloud file (PLY, LAS, etc.)\n", "    metadata_path : str, optional\n", "        Path to the metadata CSV file\n", "    metadata_type : str\n", "        Type of metadata ('ifc' or 'cad')\n", "        \n", "    Returns:\n", "    --------\n", "    workflow_results : dict\n", "        Complete workflow results\n", "    \"\"\"\n", "    print(\"=== Post-IFC Preprocessing Workflow ===\")\n", "    \n", "    # Step 1: Load point cloud\n", "    print(\"\\n1. Loading point cloud...\")\n", "    try:\n", "        if point_cloud_path.endswith('.ply'):\n", "            pcd = o3d.io.read_point_cloud(point_cloud_path)\n", "            points = np.asarray(pcd.points)\n", "        elif point_cloud_path.endswith('.las'):\n", "            import laspy\n", "            las_file = laspy.read(point_cloud_path)\n", "            points = np.vstack((las_file.x, las_file.y, las_file.z)).T\n", "        else:\n", "            raise ValueError(f\"Unsupported file format: {point_cloud_path}\")\n", "        \n", "        print(f\"Loaded point cloud: {points.shape}\")\n", "    except Exception as e:\n", "        print(f\"Error loading point cloud: {e}\")\n", "        return None\n", "    \n", "    # Step 2: Load metadata if provided\n", "    metadata_df, metadata_summary = None, None\n", "    if metadata_path:\n", "        print(\"\\n2. Loading metadata...\")\n", "        metadata_df, metadata_summary = load_extracted_metadata(metadata_path, metadata_type)\n", "    \n", "    # Step 3: Quality assessment\n", "    print(\"\\n3. Assessing point cloud quality...\")\n", "    quality_report = assess_point_cloud_quality(points, metadata_summary)\n", "    print_quality_report(quality_report)\n", "    \n", "    # Step 4: Cleaning\n", "    print(\"\\n4. Cleaning point cloud...\")\n", "    cleaned_points, cleaning_stats = clean_point_cloud(\n", "        points, \n", "        remove_outliers=quality_report['outlier_percentage'] > 2,\n", "        remove_duplicates=quality_report['duplicate_percentage'] > 0.5\n", "    )\n", "    \n", "    # Step 5: Coordinate system validation\n", "    print(\"\\n5. Validating coordinate system...\")\n", "    coord_validation = validate_coordinate_system(cleaned_points, metadata=metadata_summary)\n", "    \n", "    if coord_validation['issues']:\n", "        print(\"Coordinate system issues detected:\")\n", "        for issue in coord_validation['issues']:\n", "            print(f\"  - {issue}\")\n", "        print(\"Recommendations:\")\n", "        for rec in coord_validation['recommendations']:\n", "            print(f\"  - {rec}\")\n", "    \n", "    # Step 6: Normalization\n", "    print(\"\\n6. Normalizing coordinates...\")\n", "    normalized_points, transform_info = normalize_coordinates(cleaned_points, method='center')\n", "    \n", "    # Step 7: Optimization for alignment\n", "    print(\"\\n7. Optimizing for alignment...\")\n", "    optimized_points, optimization_stats = optimize_for_alignment(\n", "        normalized_points, \n", "        max_points=50000,\n", "        voxel_size='auto'\n", "    )\n", "    \n", "    # Step 8: Metadata integration\n", "    integrated_data = None\n", "    if metadata_df is not None:\n", "        print(\"\\n8. Integrating metadata...\")\n", "        integrated_data = integrate_metadata_with_pointcloud(\n", "            optimized_points, metadata_df, metadata_type\n", "        )\n", "    \n", "    # Compile results\n", "    workflow_results = {\n", "        'original_points': points,\n", "        'cleaned_points': cleaned_points,\n", "        'normalized_points': normalized_points,\n", "        'optimized_points': optimized_points,\n", "        'quality_report': quality_report,\n", "        'cleaning_stats': cleaning_stats,\n", "        'coord_validation': coord_validation,\n", "        'transform_info': transform_info,\n", "        'optimization_stats': optimization_stats,\n", "        'metadata_summary': metadata_summary,\n", "        'integrated_data': integrated_data\n", "    }\n", "    \n", "    print(\"\\n=== Workflow Complete ===\")\n", "    print(f\"Final point cloud: {len(optimized_points)} points\")\n", "    print(f\"Ready for alignment workflow!\")\n", "    \n", "    return workflow_results\n", "\n", "# Example usage:\n", "# results = complete_post_ifc_workflow(\n", "#     point_cloud_path='data/ifc_pointcloud.ply',\n", "#     metadata_path='data/ifc_metadata.csv',\n", "#     metadata_type='ifc'\n", "# )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}