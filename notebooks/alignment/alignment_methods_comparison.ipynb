{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Point Cloud Alignment Methods Comparison: ICP vs Neural Networks\n", "\n", "This notebook provides a comprehensive comparison between ICP-based and neural network-based point cloud alignment methods. It analyzes results from both approaches to determine the most effective method for point cloud registration tasks.\n", "\n", "**Stage**: Alignment Analysis  \n", "**Input Data**: Results from ICP and Neural Network alignment notebooks  \n", "**Output**: Comparative analysis, performance metrics, and method recommendations  \n", "**Format**: Comprehensive evaluation report with visualizations  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Research Questions:\n", "1. **Which method provides better alignment accuracy?**\n", "2. **What are the computational trade-offs between ICP and neural networks?**\n", "3. **Which approach is more robust to varying point cloud conditions?**\n", "4. **How do the methods perform with different data types and noise levels?**\n", "5. **What are the practical recommendations for method selection?**\n", "\n", "## Comparison Framework:\n", "1. **Load Results**: Import alignment results from both methods\n", "2. **Performance Metrics**: Calculate accuracy, precision, and error metrics\n", "3. **Computational Analysis**: Compare processing time and resource usage\n", "4. **Robustness Evaluation**: Analyze performance across different conditions\n", "5. **Statistical Analysis**: Perform statistical tests on results\n", "6. **Visualization**: Create comprehensive comparison plots\n", "7. **Recommendations**: Provide method selection guidelines"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment for comparative analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install pandas numpy mat<PERSON><PERSON><PERSON>b seaborn plotly scipy scikit-learn\n", "!pip install statsmodels open3d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "from pathlib import Path\n", "import json\n", "import time\n", "from datetime import datetime\n", "import open3d as o3d\n", "\n", "# Statistical analysis\n", "from scipy import stats\n", "from scipy.stats import ttest_ind, mannwhitneyu, chi2_contingency\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style for plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Alignment Methods Comparison Framework Initialized\")\n", "print(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration and Data Paths\n", "\n", "Define paths and parameters for the comparison analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ComparisonConfig:\n", "    \"\"\"\n", "    Configuration for alignment methods comparison.\n", "    \"\"\"\n", "    \n", "    # Project configuration\n", "    PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "    PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "    \n", "    # Comparison parameters\n", "    DISTANCE_THRESHOLD = 0.1  # For accuracy evaluation\n", "    CONFIDENCE_LEVEL = 0.95  # For statistical tests\n", "    \n", "    # Visualization parameters\n", "    FIGURE_SIZE = (12, 8)\n", "    DPI = 300\n", "    \n", "    def __init__(self):\n", "        self.base_path = Path('../..')\n", "        self.data_path = self.base_path / 'data'\n", "        self.alignment_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'alignment'\n", "        self.results_path = self.alignment_path / 'comparison_results'\n", "        self.results_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        # Method-specific paths\n", "        self.icp_results_path = self.alignment_path / 'icp_results'\n", "        self.nn_results_path = self.alignment_path / 'neural_network_results'\n", "        \n", "        print(f\"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}\")\n", "        print(f\"Alignment path: {self.alignment_path}\")\n", "        print(f\"Results path: {self.results_path}\")\n", "        print(f\"ICP results: {self.icp_results_path}\")\n", "        print(f\"NN results: {self.nn_results_path}\")\n", "\n", "config = ComparisonConfig()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Loading Functions\n", "\n", "Load results from both alignment methods for comparison."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_alignment_results(method_name, results_path):\n", "    \"\"\"\n", "    Load alignment results from a specific method.\n", "    \n", "    Parameters:\n", "    -----------\n", "    method_name : str\n", "        Name of the alignment method ('ICP' or 'Neural Network')\n", "    results_path : Path\n", "        Path to the results directory\n", "    \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing alignment results and metadata\n", "    \"\"\"\n", "    print(f\"Loading {method_name} results from {results_path}\")\n", "    \n", "    results = {\n", "        'method': method_name,\n", "        'metadata': None,\n", "        'aligned_points': None,\n", "        'transformation_matrix': None,\n", "        'performance_metrics': {},\n", "        'timing_info': {}\n", "    }\n", "    \n", "    try:\n", "        # Load metadata\n", "        metadata_files = list(results_path.glob('*metadata*.json'))\n", "        if metadata_files:\n", "            with open(metadata_files[0], 'r') as f:\n", "                results['metadata'] = json.load(f)\n", "                print(f\"Loaded metadata from {metadata_files[0]}\")\n", "        \n", "        # Load aligned point clouds\n", "        point_cloud_files = list(results_path.glob('*aligned*.pcd')) + list(results_path.glob('*aligned*.ply'))\n", "        if point_cloud_files:\n", "            pcd = o3d.io.read_point_cloud(str(point_cloud_files[0]))\n", "            results['aligned_points'] = np.asarray(pcd.points)\n", "            print(f\"Loaded {results['aligned_points'].shape[0]} aligned points\")\n", "        \n", "        # Extract performance metrics from metadata\n", "        if results['metadata']:\n", "            if 'alignment_quality' in results['metadata']:\n", "                results['performance_metrics'] = results['metadata']['alignment_quality']\n", "            if 'timing_info' in results['metadata']:\n", "                results['timing_info'] = results['metadata']['timing_info']\n", "            if 'transformation_matrix' in results['metadata']:\n", "                results['transformation_matrix'] = np.array(results['metadata']['transformation_matrix'])\n", "        \n", "        print(f\"Successfully loaded {method_name} results\")\n", "        return results\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading {method_name} results: {e}\")\n", "        return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_synthetic_results():\n", "    \"\"\"\n", "    Create synthetic results for demonstration purposes.\n", "    This function generates realistic alignment results when actual results are not available.\n", "    \"\"\"\n", "    print(\"Creating synthetic alignment results for demonstration...\")\n", "    \n", "    # Generate synthetic point clouds\n", "    np.random.seed(42)\n", "    n_points = 1000\n", "    \n", "    # Create source point cloud (cube)\n", "    source_points = np.random.rand(n_points, 3) * 2 - 1\n", "    \n", "    # Create target with known transformation\n", "    angle = np.radians(15)\n", "    R_true = np.array([\n", "        [np.cos(angle), -np.sin(angle), 0],\n", "        [np.sin(angle), np.cos(angle), 0],\n", "        [0, 0, 1]\n", "    ])\n", "    t_true = np.array([0.2, 0.1, 0.05])\n", "    target_points = np.dot(source_points, R_true.T) + t_true\n", "    \n", "    # Simulate ICP results\n", "    icp_results = {\n", "        'method': 'ICP',\n", "        'aligned_points': target_points + np.random.normal(0, 0.01, target_points.shape),\n", "        'transformation_matrix': np.eye(4),\n", "        'performance_metrics': {\n", "            'final_mse': 0.0023,\n", "            'iterations': 28,\n", "            'converged': True,\n", "            'rmse': 0.048,\n", "            'translation_error': 0.012,\n", "            'rotation_error': 0.8\n", "        },\n", "        'timing_info': {\n", "            'total_time': 2.34,\n", "            'preprocessing_time': 0.45,\n", "            'alignment_time': 1.89\n", "        }\n", "    }\n", "    \n", "    # Simulate Neural Network results\n", "    nn_results = {\n", "        'method': 'Neural Network',\n", "        'aligned_points': target_points + np.random.normal(0, 0.015, target_points.shape),\n", "        'transformation_matrix': np.eye(4),\n", "        'performance_metrics': {\n", "            'final_loss': 0.0034,\n", "            'epochs': 85,\n", "            'converged': True,\n", "            'rmse': 0.061,\n", "            'translation_error': 0.018,\n", "            'rotation_error': 1.2\n", "        },\n", "        'timing_info': {\n", "            'total_time': 45.67,\n", "            'training_time': 42.1,\n", "            'inference_time': 0.12,\n", "            'preprocessing_time': 3.45\n", "        }\n", "    }\n", "    \n", "    return icp_results, nn_results, source_points, target_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON> and <PERSON>pare Data\n", "\n", "Load alignment results from both methods for comparison."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to load actual results, fall back to synthetic data if not available\n", "try:\n", "    print(\"Attempting to load actual alignment results...\")\n", "    icp_results = load_alignment_results(\"ICP\", config.icp_results_path)\n", "    nn_results = load_alignment_results(\"Neural Network\", config.nn_results_path)\n", "    \n", "    # Check if we have valid results\n", "    if (icp_results['aligned_points'] is None or \n", "        nn_results['aligned_points'] is None or\n", "        not icp_results['performance_metrics'] or\n", "        not nn_results['performance_metrics']):\n", "        raise ValueError(\"Incomplete results found\")\n", "    \n", "    print(\"Successfully loaded actual alignment results\")\n", "    source_points = None  # Will be loaded from metadata if available\n", "    target_points = None\n", "    \n", "except Exception as e:\n", "    print(f\"Could not load actual results: {e}\")\n", "    print(\"Generating synthetic results for demonstration...\")\n", "    icp_results, nn_results, source_points, target_points = create_synthetic_results()\n", "\n", "print(f\"\\nComparison Data Summary:\")\n", "print(f\"ICP Method: {icp_results['method']}\")\n", "print(f\"Neural Network Method: {nn_results['method']}\")\n", "if icp_results['aligned_points'] is not None:\n", "    print(f\"ICP aligned points: {icp_results['aligned_points'].shape[0]}\")\n", "if nn_results['aligned_points'] is not None:\n", "    print(f\"NN aligned points: {nn_results['aligned_points'].shape[0]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Side-by-Side Performance Comparison\n", "\n", "Create comprehensive side-by-side comparison visualizations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comparison DataFrame\n", "comparison_data = {\n", "    'Metric': [],\n", "    'ICP': [],\n", "    'Neural Network': [],\n", "    'Unit': [],\n", "    'Better': []  # Which method performs better\n", "}\n", "\n", "# Performance metrics comparison\n", "metrics_to_compare = [\n", "    ('RMSE', 'rmse', 'lower'),\n", "    ('Mean Distance', 'mean_distance', 'lower'),\n", "    ('Translation Error', 'translation_error', 'lower'),\n", "    ('Rotation Error', 'rotation_error', 'lower'),\n", "    ('Total Time', 'total_time', 'lower'),\n", "    ('Convergence', 'converged', 'higher')\n", "]\n", "\n", "for metric_name, metric_key, better_direction in metrics_to_compare:\n", "    icp_val = icp_results['performance_metrics'].get(metric_key, \n", "                                                   icp_results['timing_info'].get(metric_key, 'N/A'))\n", "    nn_val = nn_results['performance_metrics'].get(metric_key, \n", "                                                  nn_results['timing_info'].get(metric_key, 'N/A'))\n", "    \n", "    comparison_data['Metric'].append(metric_name)\n", "    comparison_data['ICP'].append(icp_val)\n", "    comparison_data['Neural Network'].append(nn_val)\n", "    \n", "    # Determine units\n", "    if 'time' in metric_key.lower():\n", "        unit = 'seconds'\n", "    elif 'error' in metric_key.lower() or 'rmse' in metric_key.lower() or 'distance' in metric_key.lower():\n", "        unit = 'units'\n", "    else:\n", "        unit = ''\n", "    comparison_data['Unit'].append(unit)\n", "    \n", "    # Determine which is better\n", "    if isinstance(icp_val, (int, float)) and isinstance(nn_val, (int, float)):\n", "        if better_direction == 'lower':\n", "            better = 'ICP' if icp_val < nn_val else 'Neural Network'\n", "        else:\n", "            better = 'ICP' if icp_val > nn_val else 'Neural Network'\n", "    else:\n", "        better = 'Tie'\n", "    comparison_data['Better'].append(better)\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "print(\"\\nPerformance Comparison Summary:\")\n", "print(comparison_df.to_string(index=False))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}