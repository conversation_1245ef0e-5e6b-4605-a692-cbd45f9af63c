# Install required packages
!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas ifcopenshell ifcopenshell-geom ezdxf

# Import libraries
import numpy as np
import os
import json
import matplotlib.pyplot as plt
import open3d as o3d
import laspy
import logging
import time
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree
import pandas as pd

# Try to import ifcopenshell for IFC files
try:
    import ifcopenshell
    import ifcopenshell.geom
    IFC_SUPPORT = True
except ImportError:
    print("⚠️ ifcopenshell not installed. IFC files will not be supported.")
    IFC_SUPPORT = False

# Try to import TensorFlow for neural network approaches
try:
    import tensorflow as tf
    tf.random.set_seed(42)
    TF_SUPPORT = True
    print(f"✅ TensorFlow available: {tf.__version__}")
except ImportError:
    print("⚠️ TensorFlow not available. Neural network methods will be disabled.")
    TF_SUPPORT = False

# Set random seeds for reproducibility
np.random.seed(42)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set up paths with proper project organization
base_path = Path('../..')  # Adjust to your project root
data_path = base_path / 'data'

# Project organization - adjust based on your project
PROJECT_TYPE = "ENEL"  # Options: "ENEL", "USA"
PROJECT_NAME = "Trino"  # ENEL: Trino, Castro, Mudjar, Giorgio | USA: McCarthy, RPCS, RES

# Input and output paths following the specified organization
preprocessing_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'preprocessing'
alignment_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'alignment'
alignment_path.mkdir(parents=True, exist_ok=True)

print("🔄 IFC Point Cloud Alignment - Ready!")
print(f"📁 Data path: {data_path}")
print(f"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}")
print(f"📥 Input path: {preprocessing_path}")
print(f"💾 Output path: {alignment_path}")
print(f"🔧 Open3D version: {o3d.__version__}")

def load_processed_point_cloud():
    """
    Load processed point cloud data from various sources.
    Expected input: Processed point cloud from ground segmentation or preprocessing.
    """
    print(f"🔍 Loading processed point cloud data...")
    
    # Try different point cloud sources in order of preference
    point_cloud_sources = [
        # From ground segmentation stage (preferred)
        data_path / PROJECT_TYPE / PROJECT_NAME / 'ground_segmentation' / f'{PROJECT_NAME}_non_ground_points.ply',
        data_path / PROJECT_TYPE / PROJECT_NAME / 'ground_segmentation' / f'{PROJECT_NAME}_non_ground_points.pcd',
        # From raw data (fallback)
        data_path / PROJECT_TYPE / PROJECT_NAME / 'raw' / f'{PROJECT_NAME}_point_cloud.las',
        data_path / PROJECT_TYPE / PROJECT_NAME / 'raw' / f'{PROJECT_NAME}_point_cloud.ply',
        data_path / PROJECT_TYPE / PROJECT_NAME / 'raw' / f'{PROJECT_NAME}_point_cloud.pcd',
        # Legacy paths for backward compatibility
        Path('/content/gdrive/MyDrive/pc-experiment/Trino_Fly_2_Shifted.las')
    ]
    
    for pc_path in point_cloud_sources:
        if pc_path.exists():
            print(f"✅ Found point cloud: {pc_path}")
            
            try:
                if pc_path.suffix.lower() == '.las':
                    # Load LAS file
                    las_file = laspy.read(str(pc_path))
                    points = np.vstack((las_file.x, las_file.y, las_file.z)).T
                    print(f"📊 Loaded {points.shape[0]:,} points from LAS file")
                    
                elif pc_path.suffix.lower() in ['.ply', '.pcd']:
                    # Load PLY/PCD file using Open3D
                    pcd = o3d.io.read_point_cloud(str(pc_path))
                    points = np.asarray(pcd.points)
                    print(f"📊 Loaded {points.shape[0]:,} points from {pc_path.suffix.upper()} file")
                    
                    # Check for colors
                    if pcd.has_colors():
                        colors = np.asarray(pcd.colors)
                        print(f"🎨 Point cloud includes RGB color information")
                    
                else:
                    print(f"⚠️ Unsupported file format: {pc_path.suffix}")
                    continue
                
                return points
                
            except Exception as e:
                print(f"❌ Error loading {pc_path}: {e}")
                continue
    
    print(f"❌ No processed point cloud found in expected locations")
    return None

def load_ifc_cad_geometry():
    """
    Load IFC mesh or CAD geometry from preprocessing stage.
    Expected input: IFC/CAD metadata with geometry information.
    """
    print(f"🔍 Loading IFC/CAD geometry data...")
    
    # Try different geometry sources in order of preference
    geometry_sources = [
        # IFC metadata with geometry (preferred)
        preprocessing_path / f'{PROJECT_NAME}_ifc_metadata.json',
        # CAD metadata with geometry
        preprocessing_path / f'{PROJECT_NAME}_cad_metadata.json',
        # Raw IFC files (fallback)
        data_path / PROJECT_TYPE / PROJECT_NAME / 'raw' / f'{PROJECT_NAME}.ifc',
        # Legacy paths for backward compatibility
        Path('/content/gdrive/MyDrive/pc-experiment/GRE.EEC.S.00.IT.P.14353.00.265.ifc')
    ]
    
    for geom_path in geometry_sources:
        if geom_path.exists():
            print(f"✅ Found geometry source: {geom_path}")
            
            try:
                if geom_path.suffix.lower() == '.json':
                    # Load metadata and extract pile positions
                    with open(geom_path, 'r') as f:
                        metadata = json.load(f)
                    
                    if 'pile_elements' in metadata:
                        pile_points = []
                        for pile in metadata['pile_elements']:
                            if 'LocalCoordinates' in pile:
                                coords = pile['LocalCoordinates']
                                pile_points.append([coords['X_Local'], coords['Y_Local'], coords['Z_Local']])
                        
                        if pile_points:
                            points = np.array(pile_points)
                            print(f"📊 Extracted {points.shape[0]:,} pile positions from metadata")
                            return points
                    
                elif geom_path.suffix.lower() == '.ifc' and IFC_SUPPORT:
                    # Load IFC file directly
                    ifc_file = ifcopenshell.open(str(geom_path))
                    
                    # Create settings for geometry processing
                    settings = ifcopenshell.geom.settings()
                    settings.set(settings.USE_WORLD_COORDS, True)
                    
                    all_vertices = []
                    
                    # Extract geometry from pile elements
                    for element in ifc_file.by_type('IfcPile'):
                        if element.Representation:
                            try:
                                shape = ifcopenshell.geom.create_shape(settings, element)
                                vertices = shape.geometry.verts
                                vertices_array = np.array(vertices).reshape(-1, 3)
                                all_vertices.append(vertices_array)
                            except Exception as e:
                                logger.warning(f"Could not process element {element.id()}: {e}")
                                continue
                    
                    if all_vertices:
                        points = np.vstack(all_vertices)
                        print(f"📊 Extracted {points.shape[0]:,} points from IFC geometry")
                        return points
                
                else:
                    print(f"⚠️ Unsupported geometry format: {geom_path.suffix}")
                    continue
                    
            except Exception as e:
                print(f"❌ Error loading {geom_path}: {e}")
                continue
    
    print(f"❌ No IFC/CAD geometry found in expected locations")
    return None

def normalize_point_cloud(points):
    """
    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.
    """
    if points is None or len(points) == 0:
        return None
    
    # Center the point cloud
    centroid = np.mean(points, axis=0)
    centered = points - centroid
    
    # Scale to unit sphere
    max_distance = np.max(np.linalg.norm(centered, axis=1))
    if max_distance > 0:
        normalized = centered / max_distance
    else:
        normalized = centered
    
    return normalized

def downsample_voxel(points, voxel_size=0.05):
    """
    Downsample point cloud using voxel grid.
    """
    if points is None or len(points) == 0:
        return None
    
    # Create Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # Downsample
    downsampled = pcd.voxel_down_sample(voxel_size)
    
    return np.asarray(downsampled.points)

def nearest_neighbor(source, target):
    """
    Find nearest neighbors between source and target point clouds.
    """
    tree = cKDTree(target)
    distances, indices = tree.query(source)
    return distances, indices

def best_fit_transform(source, target):
    """
    Calculates the least-squares best-fit transform between corresponding 3D points.
    """
    assert source.shape == target.shape, "Source and target point clouds must have the same shape"

    # Center both point clouds
    source_centroid = np.mean(source, axis=0)
    target_centroid = np.mean(target, axis=0)
    source_centered = source - source_centroid
    target_centered = target - target_centroid

    # Compute covariance matrix H
    H = np.dot(source_centered.T, target_centered)

    # Singular Value Decomposition
    U, S, Vt = np.linalg.svd(H)

    # Compute rotation matrix R
    R = np.dot(Vt.T, U.T)

    # Ensure proper rotation (det(R) = 1)
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = np.dot(Vt.T, U.T)

    # Compute translation
    t = target_centroid - np.dot(R, source_centroid)

    # Create homogeneous transformation matrix
    T = np.identity(4)
    T[:3, :3] = R
    T[:3, 3] = t

    return T, R, t

def icp_algorithm(source, target, max_iterations=50, tolerance=1e-6, verbose=False):
    """
    Iterative Closest Point (ICP) algorithm for point cloud alignment.
    """
    # Make a copy of the source point cloud
    source_copy = np.copy(source)
    prev_error = 0

    # Initialize transformation matrix
    T_combined = np.identity(4)

    start_time = time.time()

    for iteration in range(max_iterations):
        # Find nearest neighbors
        distances, indices = nearest_neighbor(source_copy, target)

        # Compute mean squared error
        mean_error = np.mean(distances**2)

        # Check for convergence
        if verbose:
            print(f"Iteration {iteration+1}, MSE: {mean_error:.10f}")

        if abs(prev_error - mean_error) < tolerance:
            if verbose:
                print(f"Converged after {iteration+1} iterations.")
            break

        prev_error = mean_error

        # Get corresponding points
        corresponding_target_points = target[indices]

        # Compute transformation
        T, R, t = best_fit_transform(source_copy, corresponding_target_points)

        # Update transformation matrix
        T_combined = np.dot(T, T_combined)

        # Apply transformation
        source_copy = np.dot(source_copy, R.T) + t

    end_time = time.time()

    if verbose:
        print(f"ICP completed in {end_time - start_time:.4f} seconds")
        if iteration == max_iterations - 1:
            print(f"Warning: Maximum iterations ({max_iterations}) reached without convergence.")

    return T_combined, source_copy, mean_error, iteration + 1

def visualize_point_cloud(points, title="Point Cloud", point_size=1.0, color='blue'):
    """
    Visualize a single point cloud using matplotlib.
    """
    if points is None or len(points) == 0:
        print("No points to visualize")
        return
    
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    ax.scatter(points[:, 0], points[:, 1], points[:, 2], 
              c=color, s=point_size, alpha=0.6)
    
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title(title)
    
    plt.tight_layout()
    plt.show()

def visualize_point_clouds_comparison(source, target, aligned_source=None, title="Point Cloud Comparison"):
    """
    Visualize comparison between source, target, and optionally aligned source point clouds.
    """
    try:
        if aligned_source is not None:
            fig = plt.figure(figsize=(18, 6))
            
            # Original source vs target
            ax1 = fig.add_subplot(131, projection='3d')
            ax1.scatter(source[:, 0], source[:, 1], source[:, 2], c='blue', s=1, alpha=0.6, label='Source')
            ax1.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')
            ax1.set_title('Before Alignment')
            ax1.legend()
            
            # Aligned source vs target
            ax2 = fig.add_subplot(132, projection='3d')
            ax2.scatter(aligned_source[:, 0], aligned_source[:, 1], aligned_source[:, 2], c='green', s=1, alpha=0.6, label='Aligned Source')
            ax2.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')
            ax2.set_title('After Alignment')
            ax2.legend()
            
            # Overlay comparison
            ax3 = fig.add_subplot(133, projection='3d')
            ax3.scatter(source[:, 0], source[:, 1], source[:, 2], c='blue', s=1, alpha=0.3, label='Original Source')
            ax3.scatter(aligned_source[:, 0], aligned_source[:, 1], aligned_source[:, 2], c='green', s=1, alpha=0.6, label='Aligned Source')
            ax3.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')
            ax3.set_title('Overlay Comparison')
            ax3.legend()
        else:
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(source[:, 0], source[:, 1], source[:, 2], c='blue', s=1, alpha=0.6, label='Source')
            ax.scatter(target[:, 0], target[:, 1], target[:, 2], c='red', s=1, alpha=0.6, label='Target')
            ax.set_title('Point Cloud Comparison')
            ax.legend()

        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        plt.show()
    except Exception as e:
        print("Error occurred during comparison visualization:", e)

# Define paths to your files
if IN_COLAB:
    base_path = '/content/gdrive/MyDrive/pc-experiment'
else:
    base_path = 'data/pc-experiment'

# File paths
las_file = f"{base_path}/Trino_Fly_2_Shifted.las"
ifc_file = f"{base_path}/GRE.EEC.S.00.IT.P.14353.00.265.ifc"

print(f"LAS file: {las_file}")
print(f"IFC file: {ifc_file}")

# Load point cloud data
try:
    target_points = read_las_file(las_file)
    print(f"Loaded target point cloud: {target_points.shape if target_points is not None else 'Failed'}")

    if IFC_SUPPORT:
        source_points = read_ifc_file(ifc_file)
        print(f"Loaded source point cloud: {source_points.shape if source_points is not None else 'Failed'}")
    else:
        print("IFC support not available. Creating synthetic data.")
        source_points = None

    # Create synthetic data if needed
    if source_points is None or target_points is None:
        print("Creating synthetic data...")
        if target_points is not None:
            # Create synthetic source from target
            angle = np.radians(15)
            R = np.array([
                [np.cos(angle), -np.sin(angle), 0],
                [np.sin(angle), np.cos(angle), 0],
                [0, 0, 1]
            ])
            t = np.array([0.5, 0.3, 0.2])
            source_points = np.dot(target_points, R) - t
        else:
            # Create completely synthetic data
            x, y, z = np.meshgrid(np.linspace(-1, 1, 10), np.linspace(-1, 1, 10), np.linspace(-1, 1, 10))
            source_points = np.vstack((x.flatten(), y.flatten(), z.flatten())).T
            
            angle = np.radians(15)
            R = np.array([
                [np.cos(angle), -np.sin(angle), 0],
                [np.sin(angle), np.cos(angle), 0],
                [0, 0, 1]
            ])
            t = np.array([0.5, 0.3, 0.2])
            target_points = np.dot(source_points, R.T) + t

except Exception as e:
    print(f"Error loading point clouds: {e}")
    # Fallback synthetic data
    source_points = np.random.rand(1000, 3)
    target_points = source_points + 0.1 * np.random.rand(1000, 3)

# Normalize and downsample
source_pc = normalize_point_cloud(source_points)
target_pc = normalize_point_cloud(target_points)

print(f"Source shape: {source_pc.shape}")
print(f"Target shape: {target_pc.shape}")

# Downsample for faster processing
voxel_size = 0.02 if source_pc.shape[0] > 10000 else 0.05
subsampled_source_pc = downsample_voxel(source_pc, voxel_size=voxel_size)
subsampled_target_pc = downsample_voxel(target_pc, voxel_size=voxel_size)

print(f"Downsampled Source shape: {subsampled_source_pc.shape}")
print(f"Downsampled Target shape: {subsampled_target_pc.shape}")

# Visualize initial point clouds
visualize_point_clouds_comparison(subsampled_source_pc, subsampled_target_pc,
                                 title="Source vs Target Point Clouds (Before Alignment)")

# Run ICP algorithm
T_icp, aligned_source_icp, icp_error, icp_iterations = icp_algorithm(
    subsampled_source_pc, subsampled_target_pc, max_iterations=50, tolerance=1e-6, verbose=True)

print(f"\nFinal ICP transformation matrix:\n{T_icp}")
print(f"Final mean squared error: {icp_error:.10f}")

# Visualize alignment result
visualize_point_clouds_comparison(subsampled_source_pc, subsampled_target_pc, aligned_source_icp,
                                 title="ICP Alignment Result")

# Apply transformation to full resolution point cloud
if target_points is not None and 'T_icp' in locals():
    print(f"🔄 Applying alignment transformation to full resolution point cloud...")
    
    # Apply the transformation matrix to the full target point cloud
    # Convert to homogeneous coordinates
    target_homogeneous = np.hstack([target_points, np.ones((target_points.shape[0], 1))])
    
    # Apply transformation
    aligned_full_homogeneous = np.dot(target_homogeneous, T_icp.T)
    aligned_full_points = aligned_full_homogeneous[:, :3]
    
    print(f"📊 Aligned point cloud shape: {aligned_full_points.shape}")
    
    # Create Open3D point cloud for export
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_full_points)
    
    # Export aligned point cloud in .pcd format (binary for high fidelity)
    output_filename = f'{PROJECT_NAME}_aligned_point_cloud.pcd'
    output_path_full = alignment_path / output_filename
    
    # Save in binary format for high fidelity (works well for ICP/PointNet)
    success = o3d.io.write_point_cloud(str(output_path_full), aligned_pcd, write_ascii=False)
    
    if success:
        print(f"✅ Aligned point cloud exported: {output_path_full}")
        print(f"   Format: .pcd (binary, high fidelity)")
        print(f"   Points: {aligned_full_points.shape[0]:,}")
    else:
        print(f"❌ Failed to export aligned point cloud")
    
    # Also save alignment metadata
    alignment_metadata = {
        'project_info': {
            'project_name': PROJECT_NAME,
            'project_type': PROJECT_TYPE,
            'alignment_timestamp': datetime.now().isoformat()
        },
        'alignment_method': 'ICP',
        'transformation_matrix': T_icp.tolist(),
        'alignment_quality': {
            'final_mse': float(icp_error),
            'iterations': int(icp_iterations),
            'converged': icp_iterations < 50
        },
        'input_data': {
            'source_points': source_points.shape[0] if source_points is not None else 0,
            'target_points': target_points.shape[0] if target_points is not None else 0
        },
        'output_data': {
            'aligned_points': aligned_full_points.shape[0],
            'output_format': 'pcd_binary',
            'output_file': output_filename
        }
    }
    
    # Save alignment metadata
    metadata_path = alignment_path / f'{PROJECT_NAME}_alignment_metadata.json'
    with open(metadata_path, 'w') as f:
        json.dump(alignment_metadata, f, indent=2)
    
    print(f"💾 Alignment metadata saved: {metadata_path}")
    
    print(f"\n✅ Alignment stage complete! Output files:")
    print(f"  - Aligned point cloud: {output_filename}")
    print(f"  - Alignment metadata: {metadata_path.name}")
    
else:
    print("❌ No alignment transformation available for export")