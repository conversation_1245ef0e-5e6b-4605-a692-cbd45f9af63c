{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - can be overridden during execution\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "project_name = \"<PERSON>no\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, <PERSON> | USA: McCarthy, RPCS, RES\n", "source_file = \"source_pointcloud.las\"  # Source point cloud file\n", "target_file = \"target_pointcloud.las\"  # Target point cloud file\n", "num_points = 1024\n", "icp_max_iterations = 20\n", "icp_tolerance = 1e-6\n", "icp_voxel_size = 0.02\n", "use_downsampling = True\n", "neural_model_path = \"../../models/neural_alignment_model.h5\"\n", "output_dir = \"../../output_runs/alignment\"\n", "mlflow_experiment_name = \"alignment_hybrid\"\n", "mlflow_run_name = f\"hybrid_{project_type}_{project_name}\"\n", "save_intermediate = True\n", "enable_visualization = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hybrid Neural Network + ICP Point Cloud Alignment\n", "\n", "This notebook implements a hybrid approach that combines neural network-based coarse alignment with ICP refinement for precise point cloud registration. The hybrid method leverages the strengths of both approaches to achieve superior alignment results.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Source and target point clouds  \n", "**Output**: Aligned point cloud with combined transformation  \n", "**Method**: Neural Network (coarse) + ICP (fine-tuning)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Hybrid Approach Benefits:\n", "1. **Robust Initialization**: Neural network provides good initial alignment even with poor starting positions\n", "2. **Precise Refinement**: ICP fine-tunes the alignment for maximum accuracy\n", "3. **Best of Both Worlds**: Combines global optimization with local precision\n", "4. **Challenging Scenarios**: <PERSON><PERSON> partial overlaps and significant misalignments\n", "\n", "## Process Overview:\n", "1. **Environment Setup**: Import libraries and configure hybrid parameters\n", "2. **Data Loading**: Load source and target point clouds\n", "3. **Neural Network Stage**: Coarse alignment using trained neural network\n", "4. **ICP Refinement Stage**: Fine-tuning with traditional ICP\n", "5. **Evaluation**: Performance metrics and quality assessment\n", "6. **Visualization**: Comprehensive alignment results\n", "7. **Export**: Save aligned point clouds and metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment with required libraries for hybrid alignment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import tensorflow as tf\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import logging\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "import json\n", "import transforms3d.euler as t3d\n", "import transforms3d.quaternions as t3d_quaternions\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"Hybrid Neural Network + ICP Alignment Environment Initialized\")\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"Open3D version: {o3d.__version__}\")\n", "print(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Parameters\n", "\n", "Define hybrid alignment parameters and configuration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class HybridAlignmentConfig:\n", "    \"\"\"Configuration parameters for hybrid neural network + ICP alignment.\"\"\"\n", "    \n", "    # Data paths\n", "    PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "    PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "    \n", "    # Neural Network Parameters\n", "    NUM_POINTS = 1024  # Number of points for neural network\n", "    NN_MODEL_PATH = None  # Path to trained neural network model\n", "    \n", "    # ICP Parameters\n", "    ICP_MAX_ITERATIONS = 20  # Fewer iterations since we start with good alignment\n", "    ICP_TOLERANCE = 1e-6\n", "    ICP_VOXEL_SIZE = 0.02  # For downsampling before ICP\n", "    \n", "    # Hybrid workflow parameters\n", "    USE_DOWNSAMPLING = True  # Whether to downsample for ICP stage\n", "    SAVE_INTERMEDIATE = True  # Save intermediate results\n", "    \n", "    # Evaluation parameters\n", "    DISTANCE_THRESHOLD = 0.1  # For correspondence evaluation\n", "    \n", "    def __init__(self):\n", "        self.base_path = Path('../..')\n", "        self.data_path = self.base_path / 'data'\n", "        self.input_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'preprocessing'\n", "        self.output_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'alignment'\n", "        self.models_path = self.output_path / 'models'\n", "        self.hybrid_results_path = self.output_path / 'hybrid_results'\n", "        \n", "        # Create directories\n", "        self.output_path.mkdir(parents=True, exist_ok=True)\n", "        self.models_path.mkdir(parents=True, exist_ok=True)\n", "        self.hybrid_results_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        print(f\"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}\")\n", "        print(f\"Input path: {self.input_path}\")\n", "        print(f\"Output path: {self.output_path}\")\n", "        print(f\"Models path: {self.models_path}\")\n", "        print(f\"Hybrid results path: {self.hybrid_results_path}\")\n", "\n", "config = HybridAlignmentConfig()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Utility Functions\n", "\n", "Implement utility functions for point cloud processing and transformations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud of shape (N, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    normalized : numpy.n<PERSON>ray\n", "        Normalized point cloud of shape (N, 3)\n", "    centroid : numpy.n<PERSON><PERSON>\n", "        Original centroid for denormalization\n", "    scale : float\n", "        Original scale for denormalization\n", "    \"\"\"\n", "    # Center the point cloud at the origin\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale the point cloud to fit inside a unit sphere\n", "    furthest_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if furthest_distance > 0:\n", "        normalized = centered / furthest_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized, centroid, furthest_distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def downsample_voxel(points, voxel_size=0.02):\n", "    \"\"\"\n", "    Applies voxel grid downsampling to reduce point count while preserving structure.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud of shape (N, 3)\n", "    voxel_size : float\n", "        Size of voxel grid cells for downsampling\n", "    \n", "    Returns:\n", "    --------\n", "    downsampled : numpy.n<PERSON><PERSON>\n", "        Downsampled point cloud\n", "    \"\"\"\n", "    # Convert numpy array to Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Apply voxel downsampling\n", "    downsampled = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "    \n", "    # Convert back to numpy array\n", "    return np.asarray(downsampled.points)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def apply_transformation(points, R, t):\n", "    \"\"\"\n", "    Applies a transformation (rotation and translation) to a point cloud.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud of shape (N, 3)\n", "    R : numpy.n<PERSON><PERSON>\n", "        3x3 rotation matrix\n", "    t : numpy.n<PERSON><PERSON>\n", "        3x1 translation vector\n", "    \n", "    Returns:\n", "    --------\n", "    transformed_points : numpy.n<PERSON><PERSON>\n", "        Transformed point cloud of shape (N, 3)\n", "    \"\"\"\n", "    # Apply rotation and translation\n", "    transformed_points = np.dot(points, R.T) + t\n", "    return transformed_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Neural Network Stage Implementation\n", "\n", "Implement the neural network coarse alignment stage."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def neural_network_coarse_alignment(source_pc, target_pc, model=None, num_points=1024):\n", "    \"\"\"\n", "    Performs coarse alignment using a neural network.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source_pc : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target_pc : numpy.ndarray\n", "        Target point cloud of shape (M, 3)\n", "    model : tf.keras.Model, optional\n", "        Trained neural network model (if None, uses synthetic transformation)\n", "    num_points : int\n", "        Number of points to sample for neural network input\n", "    \n", "    Returns:\n", "    --------\n", "    aligned_source : numpy.ndarray\n", "        Coarsely aligned source point cloud\n", "    R_neural : numpy.n<PERSON><PERSON>\n", "        Rotation matrix from neural network\n", "    t_neural : numpy.n<PERSON><PERSON>\n", "        Translation vector from neural network\n", "    neural_time : float\n", "        Time taken for neural network alignment\n", "    \"\"\"\n", "    start_time = time.time()\n", "    \n", "    if model is not None:\n", "        print(\"Using trained neural network model for coarse alignment...\")\n", "        \n", "        # Sample points if necessary\n", "        if source_pc.shape[0] > num_points:\n", "            source_indices = np.random.choice(source_pc.shape[0], num_points, replace=False)\n", "            source_sample = source_pc[source_indices]\n", "        else:\n", "            source_indices = np.random.choice(source_pc.shape[0], num_points, replace=True)\n", "            source_sample = source_pc[source_indices]\n", "        \n", "        if target_pc.shape[0] > num_points:\n", "            target_indices = np.random.choice(target_pc.shape[0], num_points, replace=False)\n", "            target_sample = target_pc[target_indices]\n", "        else:\n", "            target_indices = np.random.choice(target_pc.shape[0], num_points, replace=True)\n", "            target_sample = target_pc[target_indices]\n", "        \n", "        # Reshape for model input (add batch dimension)\n", "        source_input = np.expand_dims(source_sample, axis=0)\n", "        target_input = np.expand_dims(target_sample, axis=0)\n", "        \n", "        # Predict transformation\n", "        quaternion, translation = model.predict([source_input, target_input])\n", "        \n", "        # Convert quaternion to rotation matrix\n", "        quaternion = quaternion[0]  # Remove batch dimension\n", "        translation = translation[0]  # Remove batch dimension\n", "        \n", "        # Convert quaternion to rotation matrix\n", "        R_neural = t3d_quaternions.quat2mat(quaternion)\n", "        t_neural = translation\n", "        \n", "    else:\n", "        print(\"No trained model available. Using synthetic coarse alignment...\")\n", "        \n", "        # Generate a reasonable coarse alignment (simulate neural network output)\n", "        # This would be replaced with actual neural network prediction\n", "        angle = np.radians(10)  # 10 degree rotation\n", "        R_neural = np.array([\n", "            [np.cos(angle), -np.sin(angle), 0],\n", "            [np.sin(angle), np.cos(angle), 0],\n", "            [0, 0, 1]\n", "        ])\n", "        t_neural = np.array([0.15, 0.08, 0.03])  # Small translation\n", "        \n", "        # Add some noise to simulate imperfect neural network prediction\n", "        noise_rotation = np.random.normal(0, 0.02, (3, 3))\n", "        noise_translation = np.random.normal(0, 0.01, 3)\n", "        \n", "        R_neural = R_neural + noise_rotation\n", "        t_neural = t_neural + noise_translation\n", "        \n", "        # Ensure R_neural is still a valid rotation matrix\n", "        U, _, Vt = np.linalg.svd(R_neural)\n", "        R_neural = np.dot(U, Vt)\n", "        if np.linalg.det(R_neural) < 0:\n", "            R_neural[:, -1] *= -1\n", "    \n", "    # Apply the neural network transformation to the entire source point cloud\n", "    aligned_source = apply_transformation(source_pc, R_neural, t_neural)\n", "    \n", "    neural_time = time.time() - start_time\n", "    \n", "    print(f\"Neural network coarse alignment completed in {neural_time:.4f} seconds\")\n", "    print(f\"Neural network rotation matrix:\\n{R_neural}\")\n", "    print(f\"Neural network translation vector: {t_neural}\")\n", "    \n", "    return aligned_source, R_neural, t_neural, neural_time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. ICP Refinement Stage Implementation\n", "\n", "Implement the ICP refinement stage for precise alignment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def nearest_neighbor(source, target):\n", "    \"\"\"\n", "    Find nearest neighbors between source and target point clouds.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    distances : numpy.ndarray\n", "        Distances to nearest neighbors\n", "    indices : numpy.ndarray\n", "        Indices of nearest neighbors in target\n", "    \"\"\"\n", "    tree = cKDTree(target)\n", "    distances, indices = tree.query(source)\n", "    return distances, indices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def best_fit_transform(source, target):\n", "    \"\"\"\n", "    Calculates the least-squares best-fit transform between corresponding 3D points.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source points of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target points of shape (N, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    T : numpy.n<PERSON><PERSON>\n", "        Homogeneous transformation matrix (4, 4)\n", "    R : numpy.n<PERSON><PERSON>\n", "        Rotation matrix (3, 3)\n", "    t : numpy.n<PERSON><PERSON>\n", "        Translation vector (3,)\n", "    \"\"\"\n", "    assert source.shape == target.shape, \"Source and target must have the same shape\"\n", "    \n", "    # Center both point clouds\n", "    source_centroid = np.mean(source, axis=0)\n", "    target_centroid = np.mean(target, axis=0)\n", "    source_centered = source - source_centroid\n", "    target_centered = target - target_centroid\n", "    \n", "    # Compute covariance matrix H\n", "    H = np.dot(source_centered.T, target_centered)\n", "    \n", "    # Singular Value Decomposition\n", "    U, S, Vt = np.linalg.svd(H)\n", "    \n", "    # Compute rotation matrix R\n", "    R = np.dot(Vt.T, U.T)\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = np.dot(Vt.T, U.T)\n", "    \n", "    # Compute translation\n", "    t = target_centroid - np.dot(R, source_centroid)\n", "    \n", "    # Create homogeneous transformation matrix\n", "    T = np.identity(4)\n", "    T[:3, :3] = R\n", "    T[:3, 3] = t\n", "    \n", "    return T, R, t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def icp_refinement(source, target, max_iterations=20, tolerance=1e-6, verbose=False):\n", "    \"\"\"\n", "    ICP algorithm for fine-tuning alignment after neural network coarse alignment.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3) (already coarsely aligned)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    max_iterations : int\n", "        Maximum number of ICP iterations\n", "    tolerance : float\n", "        Convergence tolerance\n", "    verbose : bool\n", "        Whether to print progress information\n", "    \n", "    Returns:\n", "    --------\n", "    T_icp : numpy.n<PERSON>ray\n", "        ICP transformation matrix (4, 4)\n", "    aligned_source : numpy.ndarray\n", "        Final aligned source point cloud\n", "    final_error : float\n", "        Final mean squared error\n", "    iterations : int\n", "        Number of iterations performed\n", "    convergence_history : list\n", "        History of error values for each iteration\n", "    \"\"\"\n", "    # Make a copy of the source point cloud\n", "    source_copy = np.copy(source)\n", "    prev_error = float('inf')\n", "    convergence_history = []\n", "    \n", "    # Initialize transformation matrix\n", "    T_icp = np.identity(4)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for iteration in range(max_iterations):\n", "        # Find nearest neighbors\n", "        distances, indices = nearest_neighbor(source_copy, target)\n", "        \n", "        # Compute mean squared error\n", "        mean_error = np.mean(distances**2)\n", "        convergence_history.append(mean_error)\n", "        \n", "        # Check for convergence\n", "        if verbose:\n", "            print(f\"ICP Iteration {iteration+1:3d}, MSE: {mean_error:.10f}\")\n", "        \n", "        if abs(prev_error - mean_error) < tolerance:\n", "            if verbose:\n", "                print(f\"ICP converged after {iteration+1} iterations.\")\n", "            break\n", "        \n", "        prev_error = mean_error\n", "        \n", "        # Get corresponding points\n", "        corresponding_target_points = target[indices]\n", "        \n", "        # Compute transformation\n", "        T, R, t = best_fit_transform(source_copy, corresponding_target_points)\n", "        \n", "        # Update transformation matrix\n", "        T_icp = np.dot(T, T_icp)\n", "        \n", "        # Apply transformation\n", "        source_copy = np.dot(source_copy, R.T) + t\n", "    \n", "    end_time = time.time()\n", "    \n", "    if verbose:\n", "        print(f\"ICP refinement completed in {end_time - start_time:.4f} seconds\")\n", "        if iteration == max_iterations - 1:\n", "            print(f\"Warning: Maximum ICP iterations ({max_iterations}) reached without convergence.\")\n", "    \n", "    return T_icp, source_copy, mean_error, iteration + 1, convergence_history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Main Hybrid Alignment Algorithm\n", "\n", "Implement the complete hybrid alignment workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def hybrid_alignment(source_pc, target_pc, model=None, config=None):\n", "    \"\"\"\n", "    Complete hybrid alignment workflow: Neural Network + ICP.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source_pc : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target_pc : numpy.ndarray\n", "        Target point cloud of shape (M, 3)\n", "    model : tf.keras.Model, optional\n", "        Trained neural network model\n", "    config : HybridAlignmentConfig, optional\n", "        Configuration parameters\n", "    \n", "    Returns:\n", "    --------\n", "    results : dict\n", "        Dictionary containing all alignment results and metadata\n", "    \"\"\"\n", "    if config is None:\n", "        config = HybridAlignmentConfig()\n", "    \n", "    print(\"\\n=== Starting Hybrid Alignment (Neural Network + ICP) ===\")\n", "    print(f\"Source points: {source_pc.shape[0]}\")\n", "    print(f\"Target points: {target_pc.shape[0]}\")\n", "    \n", "    # Stage 1: Neural Network Coarse Alignment\n", "    print(\"\\n--- Stage 1: Neural Network Coarse Alignment ---\")\n", "    aligned_source_neural, R_neural, t_neural, neural_time = neural_network_coarse_alignment(\n", "        source_pc, target_pc, model=model, num_points=config.NUM_POINTS\n", "    )\n", "    \n", "    # Optional: Downsample for ICP stage to improve performance\n", "    if config.USE_DOWNSAMPLING:\n", "        print(f\"\\nDownsampling point clouds for ICP refinement (voxel size: {config.ICP_VOXEL_SIZE})...\")\n", "        aligned_source_downsampled = downsample_voxel(aligned_source_neural, config.ICP_VOXEL_SIZE)\n", "        target_downsampled = downsample_voxel(target_pc, config.ICP_VOXEL_SIZE)\n", "        print(f\"Downsampled source: {aligned_source_downsampled.shape[0]} points\")\n", "        print(f\"Downsampled target: {target_downsampled.shape[0]} points\")\n", "        \n", "        icp_source = aligned_source_downsampled\n", "        icp_target = target_downsampled\n", "    else:\n", "        icp_source = aligned_source_neural\n", "        icp_target = target_pc\n", "    \n", "    # Stage 2: ICP Refinement\n", "    print(\"\\n--- Stage 2: ICP Refinement ---\")\n", "    icp_start_time = time.time()\n", "    T_icp, aligned_source_icp, icp_error, icp_iterations, icp_convergence = icp_refinement(\n", "        icp_source, icp_target, \n", "        max_iterations=config.ICP_MAX_ITERATIONS, \n", "        tolerance=config.ICP_TOLERANCE, \n", "        verbose=True\n", "    )\n", "    icp_time = time.time() - icp_start_time\n", "    \n", "    # Apply ICP transformation to the full resolution neural network result\n", "    if config.USE_DOWNSAMPLING:\n", "        # Extract rotation and translation from ICP transformation matrix\n", "        R_icp = T_icp[:3, :3]\n", "        t_icp = T_icp[:3, 3]\n", "        \n", "        # Apply to full resolution\n", "        aligned_source_final = apply_transformation(aligned_source_neural, R_icp, t_icp)\n", "    else:\n", "        aligned_source_final = aligned_source_icp\n", "    \n", "    # Combine transformations\n", "    T_neural = np.identity(4)\n", "    T_neural[:3, :3] = R_neural\n", "    T_neural[:3, 3] = t_neural\n", "    \n", "    T_combined = np.dot(T_icp, T_neural)\n", "    \n", "    # Calculate final metrics\n", "    tree = cKDTree(target_pc)\n", "    distances, _ = tree.query(aligned_source_final)\n", "    final_rmse = np.sqrt(np.mean(distances**2))\n", "    final_mean_distance = np.mean(distances)\n", "    \n", "    # Total time\n", "    total_time = neural_time + icp_time\n", "    \n", "    print(f\"\\n=== Hybrid Alignment Results ===\")\n", "    print(f\"Neural network time: {neural_time:.4f} seconds\")\n", "    print(f\"ICP refinement time: {icp_time:.4f} seconds\")\n", "    print(f\"Total hybrid time: {total_time:.4f} seconds\")\n", "    print(f\"ICP iterations: {icp_iterations}\")\n", "    print(f\"Final RMSE: {final_rmse:.6f}\")\n", "    print(f\"Final mean distance: {final_mean_distance:.6f}\")\n", "    \n", "    # Prepare results dictionary\n", "    results = {\n", "        'method': 'Hybrid (Neural Network + ICP)',\n", "        'aligned_points': aligned_source_final,\n", "        'transformation_matrix': T_combined,\n", "        'neural_network': {\n", "            'rotation_matrix': R_<PERSON>,\n", "            'translation_vector': t_neural,\n", "            'transformation_matrix': T_neural,\n", "            'time': neural_time\n", "        },\n", "        'icp_refinement': {\n", "            'transformation_matrix': T_icp,\n", "            'iterations': icp_iterations,\n", "            'final_error': icp_error,\n", "            'convergence_history': icp_convergence,\n", "            'time': icp_time\n", "        },\n", "        'performance_metrics': {\n", "            'rmse': final_rmse,\n", "            'mean_distance': final_mean_distance,\n", "            'neural_time': neural_time,\n", "            'icp_time': icp_time,\n", "            'total_time': total_time,\n", "            'icp_iterations': icp_iterations,\n", "            'converged': icp_iterations < config.ICP_MAX_ITERATIONS\n", "        },\n", "        'timing_info': {\n", "            'neural_network_time': neural_time,\n", "            'icp_refinement_time': icp_time,\n", "            'total_time': total_time\n", "        }\n", "    }\n", "    \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Data Loading and Execution\n", "\n", "Load point cloud data and execute hybrid alignment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load source and target point clouds\n", "print(\"Loading point cloud data for hybrid alignment...\")\n", "\n", "# Try to find point cloud files in the input directory\n", "point_cloud_files = list(config.input_path.glob('*.las')) + list(config.input_path.glob('*.pcd')) + list(config.input_path.glob('*.ply'))\n", "\n", "if len(point_cloud_files) >= 2:\n", "    # Load actual point clouds\n", "    source_file = point_cloud_files[0]\n", "    target_file = point_cloud_files[1]\n", "    \n", "    print(f\"Loading source: {source_file}\")\n", "    # Use the read_point_cloud_file function from ICP notebook\n", "    if source_file.suffix.lower() == '.las':\n", "        las_data = laspy.read(source_file)\n", "        source_points = np.column_stack((las_data.x, las_data.y, las_data.z))\n", "    else:\n", "        pcd = o3d.io.read_point_cloud(str(source_file))\n", "        source_points = np.asarray(pcd.points)\n", "    \n", "    print(f\"Loading target: {target_file}\")\n", "    if target_file.suffix.lower() == '.las':\n", "        las_data = laspy.read(target_file)\n", "        target_points = np.column_stack((las_data.x, las_data.y, las_data.z))\n", "    else:\n", "        pcd = o3d.io.read_point_cloud(str(target_file))\n", "        target_points = np.asarray(pcd.points)\n", "        \n", "else:\n", "    print(\"Point cloud files not found. Creating synthetic test data...\")\n", "    # Create synthetic point clouds for testing\n", "    np.random.seed(42)\n", "    source_points = np.random.rand(5000, 3) * 2 - 1  # Random points in [-1, 1]\n", "    \n", "    # Create target by applying a known transformation\n", "    angle = np.radians(25)  # 25 degree rotation\n", "    R_true = np.array([\n", "        [np.cos(angle), -np.sin(angle), 0],\n", "        [np.sin(angle), np.cos(angle), 0],\n", "        [0, 0, 1]\n", "    ])\n", "    t_true = np.array([0.3, 0.2, 0.1])\n", "    target_points = np.dot(source_points, R_true.T) + t_true\n", "    \n", "    # Add some noise\n", "    target_points += np.random.normal(0, 0.01, target_points.shape)\n", "\n", "print(f\"Source points shape: {source_points.shape}\")\n", "print(f\"Target points shape: {target_points.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Execute Hybrid Alignment\n", "\n", "Run the complete hybrid alignment workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Execute hybrid alignment\n", "hybrid_results = hybrid_alignment(\n", "    source_points, \n", "    target_points, \n", "    model=None,  # No trained model available, will use synthetic neural network\n", "    config=config\n", ")\n", "\n", "print(\"\\nHybrid alignment completed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Results Analysis and Visualization\n", "\n", "Analyze and visualize the hybrid alignment results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display detailed results\n", "print(\"\\n=== Detailed Hybrid Alignment Analysis ===\")\n", "print(f\"Method: {hybrid_results['method']}\")\n", "print(f\"\\nPerformance Metrics:\")\n", "for metric, value in hybrid_results['performance_metrics'].items():\n", "    if isinstance(value, float):\n", "        print(f\"  {metric}: {value:.6f}\")\n", "    else:\n", "        print(f\"  {metric}: {value}\")\n", "\n", "print(f\"\\nTiming Breakdown:\")\n", "for stage, time_val in hybrid_results['timing_info'].items():\n", "    print(f\"  {stage}: {time_val:.4f} seconds\")\n", "\n", "print(f\"\\nNeural Network Stage:\")\n", "print(f\"  Translation: {hybrid_results['neural_network']['translation_vector']}\")\n", "print(f\"  Time: {hybrid_results['neural_network']['time']:.4f} seconds\")\n", "\n", "print(f\"\\nICP Refinement Stage:\")\n", "print(f\"  Iterations: {hybrid_results['icp_refinement']['iterations']}\")\n", "print(f\"  Final Error: {hybrid_results['icp_refinement']['final_error']:.10f}\")\n", "print(f\"  Time: {hybrid_results['icp_refinement']['time']:.4f} seconds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save aligned point cloud and metadata\n", "aligned_pcd = o3d.geometry.PointCloud()\n", "aligned_pcd.points = o3d.utility.Vector3dVector(hybrid_results['aligned_points'])\n", "\n", "# Save in multiple formats\n", "output_file_pcd = config.hybrid_results_path / 'hybrid_aligned_source.pcd'\n", "output_file_ply = config.hybrid_results_path / 'hybrid_aligned_source.ply'\n", "\n", "o3d.io.write_point_cloud(str(output_file_pcd), aligned_pcd)\n", "o3d.io.write_point_cloud(str(output_file_ply), aligned_pcd)\n", "\n", "print(f\"\\nSaved aligned point cloud to:\")\n", "print(f\"  PCD: {output_file_pcd}\")\n", "print(f\"  PLY: {output_file_ply}\")\n", "\n", "# Save metadata\n", "metadata = {\n", "    'method': hybrid_results['method'],\n", "    'timestamp': datetime.now().isoformat(),\n", "    'performance_metrics': hybrid_results['performance_metrics'],\n", "    'timing_info': hybrid_results['timing_info'],\n", "    'transformation_matrix': hybrid_results['transformation_matrix'].tolist()\n", "}\n", "\n", "metadata_file = config.hybrid_results_path / 'hybrid_alignment_metadata.json'\n", "with open(metadata_file, 'w') as f:\n", "    json.dump(metadata, f, indent=2)\n", "\n", "print(f\"Saved metadata to: {metadata_file}\")\n", "print(\"\\nHybrid alignment workflow completed successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}