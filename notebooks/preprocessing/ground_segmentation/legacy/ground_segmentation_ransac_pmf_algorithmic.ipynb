{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation: RANSAC + PMF Sequential Processing\n", "\n", "**Date**: June 2025  \n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "Sequential ground segmentation combining RANSAC and Progressive Morphological Filter (PMF):\n", "\n", "1. **RANSAC**: Detects primary ground plane\n", "2. **PMF**: Refines non-ground points to recover additional ground\n", "3. **Combine**: Merges results for comprehensive ground detection\n", "\n", "**Input**: Raw point cloud (.las, .laz files)  \n", "**Output**: Ground and non-ground point clouds (.ply files)  \n", "**Method**: ransac_pmf identifier for consistent naming"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill Parameters - Override these when running with papermill\n", "\n", "# Site configuration\n", "site_name = \"Castro\"\n", "project_type = \"ENEL\"\n", "point_cloud_path = \"\"  # Leave empty for auto-detection\n", "\n", "# RANSAC parameters\n", "ransac_distance_threshold = 0.2  # Distance threshold (meters)\n", "ransac_num_iterations = 1000     # Number of iterations\n", "ransac_min_inliers_ratio = 0.05  # Minimum inlier ratio\n", "ransac_early_stop_ratio = 0.6    # Early stopping ratio\n", "\n", "# PMF parameters\n", "pmf_cell_size = 1.0              # Grid cell size (meters)\n", "pmf_max_window_size = 33         # Maximum window size\n", "pmf_slope = 0.15                 # Slope parameter (radians)\n", "\n", "# Processing parameters\n", "max_points_processing = 1000000  # Max points for processing\n", "save_intermediate_results = True # Save RANSAC-only and PMF-additional results\n", "create_visualizations = True     # Generate comparison plots"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup & Configuration"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported\n"]}], "source": ["# Import libraries\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "from scipy import ndimage\n", "from scipy.ndimage import grey_erosion, grey_dilation\n", "import open3d as o3d\n", "import laspy\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "print(\"Libraries imported\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logging configured\n"]}], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "print(\"Logging configured\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Paths configured\n", "- Input: ../../../data/ENEL/Castro/raw\n", "- Output: ../../../data/ENEL/Castro/ground_segmentation\n", "- Run: output_runs/Castro_ransac_pmf_20250618_172002\n"]}], "source": ["# Setup paths and directories\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "data_path = Path('../../..') / 'data'\n", "input_path = data_path / project_type / site_name / 'raw'\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "current_run_path = Path('output_runs') / f'{site_name}_ransac_pmf_{timestamp}'\n", "\n", "# Create directories\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Paths configured\")\n", "print(f\"- Input: {input_path}\")\n", "print(f\"- Output: {ground_seg_path}\")\n", "print(f\"- Run: {current_run_path}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:20:07,988 - INFO - RANSAC+PMF processing initialized for site: Castro\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Parameters saved to: output_runs/Castro_ransac_pmf_20250618_172002/parameters.json\n"]}], "source": ["# Save parameters for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC+PMF Sequential\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": ransac_distance_threshold,\n", "        \"num_iterations\": ransac_num_iterations,\n", "        \"min_inliers_ratio\": ransac_min_inliers_ratio,\n", "        \"early_stop_ratio\": ransac_early_stop_ratio\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": pmf_cell_size,\n", "        \"max_window_size\": pmf_max_window_size,\n", "        \"slope\": pmf_slope\n", "    }\n", "}\n", "\n", "with open(current_run_path / \"parameters.json\", 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {current_run_path / 'parameters.json'}\")\n", "logger.info(f\"RANSAC+PMF processing initialized for site: {site_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:21:16,179 - INFO - Reading LAS file: ../../../data/ENEL/Castro/raw/area4_point.las\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Found 1 LAS/LAZ file(s) - Loading: area4_point.las\n"]}], "source": ["# Discover LAS/LAZ files\n", "las_files = list(input_path.glob(\"*.las\")) + list(input_path.glob(\"*.laz\"))\n", "if not las_files:\n", "    raise FileNotFoundError(f\"No LAS/LAZ files found in {input_path}\")\n", "\n", "las_file = las_files[0]\n", "print(f\"Found {len(las_files)} LAS/LAZ file(s) - Loading: {las_file.name}\")\n", "logger.info(f\"Reading LAS file: {las_file}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 251,056,301 points\n", "  X: 707850.75 to 708080.57\n", "  Y: 4692889.07 to 4693090.37\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:22:35,484 - INFO - Loaded 251,056,301 points\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Z: 51.06 to 68.48\n"]}], "source": ["# Load point cloud from LAS file\n", "las = laspy.read(str(las_file))\n", "x, y, z = las.x, las.y, las.z\n", "points = np.column_stack((x, y, z))\n", "original_point_count = len(points)\n", "\n", "print(f\"Loaded {original_point_count:,} points\")\n", "print(f\"  X: {x.min():.2f} to {x.max():.2f}\")\n", "print(f\"  Y: {y.min():.2f} to {y.max():.2f}\")\n", "print(f\"  Z: {z.min():.2f} to {z.max():.2f}\")\n", "logger.info(f\"Loaded {original_point_count:,} points\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:23:27,208 - INFO - Downsampled from 251,056,301 to 1,000,000 points\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downsampled to 1,000,000 points for processing\n"]}], "source": ["# Downsample for processing efficiency if needed\n", "if original_point_count > max_points_processing:\n", "    indices = np.random.choice(original_point_count, max_points_processing, replace=False)\n", "    points = points[indices]\n", "    print(f\"Downsampled to {len(points):,} points for processing\")\n", "    logger.info(f\"Downsampled from {original_point_count:,} to {len(points):,} points\")\n", "else:\n", "    print(f\"Using all {len(points):,} points for processing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. RANSAC Ground Detection"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RANSAC Configuration:\n", "- Distance threshold: 0.2m\n", "- Iterations: 1000\n", "- Min inliers: 50,000 (5.0%)\n", "- Early stop: 600,000 (60.0%)\n"]}], "source": ["# Initialize RANSAC parameters\n", "n_points = len(points)\n", "min_inliers = int(n_points * ransac_min_inliers_ratio)\n", "early_stop_inliers = int(n_points * ransac_early_stop_ratio)\n", "\n", "print(f\"RANSAC Configuration:\")\n", "print(f\"- Distance threshold: {ransac_distance_threshold}m\")\n", "print(f\"- Iterations: {ransac_num_iterations}\")\n", "print(f\"- Min inliers: {min_inliers:,} ({ransac_min_inliers_ratio*100:.1f}%)\")\n", "print(f\"- Early stop: {early_stop_inliers:,} ({ransac_early_stop_ratio*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running RANSAC iterations...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["RANSAC: 100%|██████████| 1000/1000 [00:19<00:00, 51.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["RANSAC completed in 19.59 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# RANSAC algorithm - iterative plane fitting\n", "best_plane_params = None\n", "best_inliers = []\n", "max_inliers = 0\n", "start_time = time.time()\n", "\n", "print(\"Running RANSAC iterations...\")\n", "for i in tqdm(range(ransac_num_iterations), desc=\"RANSAC\"):\n", "    # Sample 3 random points to define plane\n", "    sample_indices = np.random.choice(n_points, 3, replace=False)\n", "    p1, p2, p3 = points[sample_indices]\n", "    \n", "    # Calculate plane normal using cross product\n", "    v1, v2 = p2 - p1, p3 - p1\n", "    normal = np.cross(v1, v2)\n", "    \n", "    # Skip degenerate cases (collinear points)\n", "    if np.linalg.norm(normal) < 1e-6:\n", "        continue\n", "    \n", "    # Normalize and ensure upward-facing normal\n", "    normal = normal / np.linalg.norm(normal)\n", "    if normal[2] < 0:\n", "        normal = -normal\n", "    \n", "    # Plane equation: ax + by + cz + d = 0\n", "    d = -np.dot(normal, p1)\n", "    plane_params = np.append(normal, d)\n", "    \n", "    # Calculate point-to-plane distances\n", "    distances = np.abs(np.dot(points, plane_params[:3]) + d)\n", "    inliers = np.where(distances < ransac_distance_threshold)[0]\n", "    n_inliers = len(inliers)\n", "    \n", "    # Update best plane if this one is better\n", "    if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "        best_plane_params = plane_params\n", "        best_inliers = inliers\n", "        max_inliers = n_inliers\n", "        \n", "        # Early stopping if enough inliers found\n", "        if n_inliers >= early_stop_inliers:\n", "            print(f\"\\nEarly stopping at iteration {i+1} with {n_inliers:,} inliers\")\n", "            break\n", "\n", "ransac_time = time.time() - start_time\n", "\n", "if best_plane_params is None:\n", "    raise RuntimeError(\"RANSAC failed to find a valid ground plane\")\n", "\n", "print(f\"RANSAC completed in {ransac_time:.2f} seconds\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:23:46,906 - INFO - RANSAC found 487,633 ground points in 19.59s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["RANSAC Results:\n", "- Ground points: 487,633 (48.8%)\n", "- Non-ground points: 512,367 (51.2%)\n", "- Plane: -0.003x + -0.004y + 1.000z + 19940.922 = 0\n"]}], "source": ["# Extract RANSAC ground and non-ground points\n", "ransac_ground_mask = np.zeros(len(points), dtype=bool)\n", "ransac_ground_mask[best_inliers] = True\n", "ransac_nonground_mask = ~ransac_ground_mask\n", "\n", "ransac_ground_points = points[ransac_ground_mask]\n", "ransac_nonground_points = points[ransac_nonground_mask]\n", "\n", "print(f\"RANSAC Results:\")\n", "print(f\"- Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Non-ground points: {len(ransac_nonground_points):,} ({len(ransac_nonground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Plane: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0\")\n", "\n", "logger.info(f\"RANSAC found {len(ransac_ground_points):,} ground points in {ransac_time:.2f}s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. PMF Refinement on Non-Ground Points"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PMF Configuration:\n", "- Processing 512,367 non-ground points from RANSAC\n", "- Cell size: 1.0m\n", "- Max window: 33\n", "- Slope threshold: 0.15 radians\n", "Starting PMF processing...\n"]}], "source": ["# PMF configuration\n", "print(f\"PMF Configuration:\")\n", "print(f\"- Processing {len(ransac_nonground_points):,} non-ground points from RANSAC\")\n", "print(f\"- Cell size: {pmf_cell_size}m\")\n", "print(f\"- Max window: {pmf_max_window_size}\")\n", "print(f\"- Slope threshold: {pmf_slope} radians\")\n", "\n", "if len(ransac_nonground_points) == 0:\n", "    print(\"No non-ground points to process with PMF\")\n", "    pmf_additional_ground_points = np.array([]).reshape(0, 3)\n", "    pmf_remaining_nonground_points = np.array([]).reshape(0, 3)\n", "    pmf_time = 0.0\n", "else:\n", "    print(\"Starting PMF processing...\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- Grid dimensions: 230 x 199 cells\n", "- Grid populated with 19933 non-empty cells\n"]}], "source": ["# PMF algorithm - grid-based morphological filtering\n", "if len(ransac_nonground_points) > 0:\n", "    pmf_start_time = time.time()\n", "    \n", "    # Create 2D grid from non-ground points\n", "    min_xy = np.min(ransac_nonground_points[:, :2], axis=0)\n", "    max_xy = np.max(ransac_nonground_points[:, :2], axis=0)\n", "    dims = np.ceil((max_xy - min_xy) / pmf_cell_size).astype(int)\n", "    \n", "    print(f\"- Grid dimensions: {dims[0]} x {dims[1]} cells\")\n", "    \n", "    if dims[0] <= 0 or dims[1] <= 0:\n", "        print(\"Invalid grid dimensions, skipping PMF\")\n", "        pmf_ground_mask_subset = np.array([False] * len(ransac_nonground_points))\n", "    else:\n", "        # Initialize grid with NaN\n", "        grid = np.full(dims, np.nan)\n", "        \n", "        # Populate grid with minimum Z values per cell\n", "        for i, (x, y, z) in enumerate(ransac_nonground_points):\n", "            xi = int((x - min_xy[0]) / pmf_cell_size)\n", "            yi = int((y - min_xy[1]) / pmf_cell_size)\n", "            if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "                if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:\n", "                    grid[xi, yi] = z\n", "        \n", "        print(f\"- Grid populated with {np.sum(~np.isnan(grid))} non-empty cells\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- Morphological filtering completed\n", "- Ground cells: 27102 / 45770\n"]}], "source": ["# PMF morphological operations\n", "if len(ransac_nonground_points) > 0 and dims[0] > 0 and dims[1] > 0:\n", "    # Fill holes in grid\n", "    filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)\n", "    \n", "    # Morphological opening (erosion followed by dilation)\n", "    opened = grey_dilation(\n", "        grey_erosion(filled_grid, size=pmf_max_window_size), \n", "        size=pmf_max_window_size\n", "    )\n", "    \n", "    # Calculate height differences\n", "    z_diff = filled_grid - opened\n", "    ground_mask_2d = z_diff < pmf_slope\n", "    \n", "    print(f\"- Morphological filtering completed\")\n", "    print(f\"- Ground cells: {np.sum(ground_mask_2d)} / {np.prod(dims)}\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- PMF completed in 1.42 seconds\n"]}], "source": ["# Map grid results back to points\n", "if len(ransac_nonground_points) > 0 and dims[0] > 0 and dims[1] > 0:\n", "    pmf_ground_mask_subset = []\n", "    for x, y, z in ransac_nonground_points:\n", "        xi = int((x - min_xy[0]) / pmf_cell_size)\n", "        yi = int((y - min_xy[1]) / pmf_cell_size)\n", "        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "            pmf_ground_mask_subset.append(ground_mask_2d[xi, yi])\n", "        else:\n", "            pmf_ground_mask_subset.append(False)\n", "    \n", "    pmf_ground_mask_subset = np.array(pmf_ground_mask_subset)\n", "    pmf_time = time.time() - pmf_start_time\n", "    \n", "    print(f\"- PMF completed in {pmf_time:.2f} seconds\")\n", "else:\n", "    pmf_ground_mask_subset = np.array([False] * len(ransac_nonground_points))\n", "    pmf_time = 0.0"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:23:48,386 - INFO - PMF found 91,818 additional ground points in 1.42s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["PMF Results:\n", "- Additional ground points: 91,818\n", "- Remaining non-ground: 420,549\n", "- PMF recovery rate: 17.9%\n"]}], "source": ["# Extract PMF results\n", "pmf_additional_ground_points = ransac_nonground_points[pmf_ground_mask_subset]\n", "pmf_remaining_nonground_points = ransac_nonground_points[~pmf_ground_mask_subset]\n", "\n", "print(f\"PMF Results:\")\n", "print(f\"- Additional ground points: {len(pmf_additional_ground_points):,}\")\n", "print(f\"- Remaining non-ground: {len(pmf_remaining_nonground_points):,}\")\n", "print(f\"- PMF recovery rate: {len(pmf_additional_ground_points)/len(ransac_nonground_points)*100:.1f}%\")\n", "\n", "logger.info(f\"PMF found {len(pmf_additional_ground_points):,} additional ground points in {pmf_time:.2f}s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Co<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:23:56,255 - INFO - Combined segmentation: 579,451 ground, 420,549 non-ground\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Combined Results (RANSAC + PMF):\n", "- Total ground points: 579,451 (57.9%)\n", "- Final non-ground: 420,549 (42.1%)\n", "- Total processing time: 21.00 seconds\n", "\n", "Breakdown:\n", "- RANSAC ground: 487,633 (48.8%)\n", "- PMF additional: 91,818 (9.2%)\n", "- Improvement: ****% ground coverage\n"]}], "source": ["# Combine RANSAC and PMF ground points\n", "if len(pmf_additional_ground_points) > 0:\n", "    combined_ground_points = np.vstack([ransac_ground_points, pmf_additional_ground_points])\n", "else:\n", "    combined_ground_points = ransac_ground_points\n", "\n", "combined_nonground_points = pmf_remaining_nonground_points\n", "total_processing_time = ransac_time + pmf_time\n", "\n", "print(f\"Combined Results (RANSAC + PMF):\")\n", "print(f\"- Total ground points: {len(combined_ground_points):,} ({len(combined_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Total processing time: {total_processing_time:.2f} seconds\")\n", "print(f\"\")\n", "print(f\"Breakdown:\")\n", "print(f\"- RANSAC ground: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- PMF additional: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Improvement: +{len(pmf_additional_ground_points)/len(points)*100:.1f}% ground coverage\")\n", "\n", "logger.info(f\"Combined segmentation: {len(combined_ground_points):,} ground, {len(combined_nonground_points):,} non-ground\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results summary saved to: output_runs/Castro_ransac_pmf_20250618_172002/results_summary.json\n"]}], "source": ["# Save processing results summary\n", "results_summary = {\n", "    \"processing_info\": {\n", "        \"total_points\": len(points),\n", "        \"original_points\": original_point_count,\n", "        \"total_time_seconds\": total_processing_time,\n", "        \"ransac_time_seconds\": ransac_time,\n", "        \"pmf_time_seconds\": pmf_time\n", "    },\n", "    \"ransac_results\": {\n", "        \"ground_points\": len(ransac_ground_points),\n", "        \"ground_ratio\": len(ransac_ground_points) / len(points),\n", "        \"plane_equation\": best_plane_params.tolist()\n", "    },\n", "    \"pmf_results\": {\n", "        \"additional_ground_points\": len(pmf_additional_ground_points),\n", "        \"additional_ground_ratio\": len(pmf_additional_ground_points) / len(points)\n", "    },\n", "    \"combined_results\": {\n", "        \"final_ground_points\": len(combined_ground_points),\n", "        \"final_nonground_points\": len(combined_nonground_points),\n", "        \"final_ground_ratio\": len(combined_ground_points) / len(points)\n", "    }\n", "}\n", "\n", "with open(current_run_path / \"results_summary.json\", 'w') as f:\n", "    json.dump(results_summary, f, indent=2)\n", "\n", "print(f\"Results summary saved to: {current_run_path / 'results_summary.json'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Save Outputs"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving point cloud outputs...\n"]}], "source": ["# Save function using Open3D (consistent with other notebooks)\n", "def save_ply(path, points_array, description=\"\"):\n", "    \"\"\"Save points to PLY file using Open3D.\"\"\"\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "    if description:\n", "        print(f\"Saved {description}: {len(points_array):,} points to {path.name}\")\n", "\n", "print(\"Saving point cloud outputs...\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:24:19,409 - INFO - Saved: ../../../data/ENEL/Castro/ground_segmentation/Castro_ransac_pmf_ground.ply\n", "2025-06-18 17:24:19,430 - INFO - Saved: ../../../data/ENEL/Castro/ground_segmentation/Castro_ransac_pmf_nonground.ply\n", "2025-06-18 17:24:19,465 - INFO - Saved: output_runs/Castro_ransac_pmf_20250618_172002/ground_points.ply\n", "2025-06-18 17:24:19,488 - INFO - Saved: output_runs/Castro_ransac_pmf_20250618_172002/nonground_points.ply\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved combined ground: 579,451 points to Castro_ransac_pmf_ground.ply\n", "Saved combined non-ground: 420,549 points to Castro_ransac_pmf_nonground.ply\n"]}], "source": ["# Save main results with method-specific naming\n", "method_name = \"ransac_pmf\"\n", "\n", "# Save to main ground segmentation directory\n", "ground_file = ground_seg_path / f\"{site_name}_{method_name}_ground.ply\"\n", "nonground_file = ground_seg_path / f\"{site_name}_{method_name}_nonground.ply\"\n", "\n", "save_ply(ground_file, combined_ground_points, \"combined ground\")\n", "save_ply(nonground_file, combined_nonground_points, \"combined non-ground\")\n", "\n", "# Save to run directory\n", "save_ply(current_run_path / \"ground_points.ply\", combined_ground_points)\n", "save_ply(current_run_path / \"nonground_points.ply\", combined_nonground_points)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:24:26,759 - INFO - Saved: output_runs/Castro_ransac_pmf_20250618_172002/ransac_only_ground.ply\n", "2025-06-18 17:24:26,790 - INFO - Saved: output_runs/Castro_ransac_pmf_20250618_172002/ransac_only_nonground.ply\n", "2025-06-18 17:24:26,797 - INFO - Saved: output_runs/Castro_ransac_pmf_20250618_172002/pmf_additional_ground.ply\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saving intermediate results...\n", "Saved RANSAC ground: 487,633 points to ransac_only_ground.ply\n", "Saved RANSAC non-ground: 512,367 points to ransac_only_nonground.ply\n", "Saved PMF additional ground: 91,818 points to pmf_additional_ground.ply\n", "Intermediate results saved to: output_runs/Castro_ransac_pmf_20250618_172002\n"]}], "source": ["# Save intermediate results if requested\n", "if save_intermediate_results:\n", "    print(\"Saving intermediate results...\")\n", "    \n", "    # RANSAC-only results\n", "    save_ply(current_run_path / \"ransac_only_ground.ply\", ransac_ground_points, \"RANSAC ground\")\n", "    save_ply(current_run_path / \"ransac_only_nonground.ply\", ransac_nonground_points, \"RANSAC non-ground\")\n", "    \n", "    # PMF additional results\n", "    if len(pmf_additional_ground_points) > 0:\n", "        save_ply(current_run_path / \"pmf_additional_ground.ply\", pmf_additional_ground_points, \"PMF additional ground\")\n", "    \n", "    print(f\"Intermediate results saved to: {current_run_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Visualization & Analysis"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Subsampled 50,000 points for visualization\n", "Visualization data prepared\n"]}], "source": ["# Prepare data for visualization (subsample for performance)\n", "if create_visualizations:\n", "    vis_sample_size = 50000\n", "    \n", "    if len(points) > vis_sample_size:\n", "        # Subsample points for visualization\n", "        vis_indices = np.random.choice(len(points), vis_sample_size, replace=False)\n", "        vis_points = points[vis_indices]\n", "        \n", "        # Create corresponding masks for visualization\n", "        vis_ransac_ground = ransac_ground_mask[vis_indices]\n", "        \n", "        print(f\"Subsampled {len(vis_points):,} points for visualization\")\n", "    else:\n", "        vis_points = points\n", "        vis_ransac_ground = ransac_ground_mask\n", "        print(f\"Using all {len(vis_points):,} points for visualization\")\n", "    \n", "    print(\"Visualization data prepared\")\n", "else:\n", "    print(\"Visualization disabled - skipping plots\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined visualization mask created\n", "- RANSAC ground: 24,257 points\n", "- Combined ground: 28,837 points\n", "- PMF improvement: +4,580 points\n"]}], "source": ["# Create combined ground mask for visualization\n", "if create_visualizations:\n", "    vis_combined_ground = np.zeros(len(vis_points), dtype=bool)\n", "    \n", "    # <PERSON> ground points\n", "    vis_combined_ground[vis_ransac_ground] = True\n", "    \n", "    # <PERSON> additional ground points\n", "    if len(pmf_additional_ground_points) > 0:\n", "        for i, point in enumerate(vis_points):\n", "            if not vis_ransac_ground[i]:  # Only check non-RANSAC points\n", "                # Check if this point is in PMF additional ground\n", "                distances = np.linalg.norm(pmf_additional_ground_points - point, axis=1)\n", "                if len(distances) > 0 and np.min(distances) < 0.1:  # Small tolerance\n", "                    vis_combined_ground[i] = True\n", "    \n", "    print(f\"Combined visualization mask created\")\n", "    print(f\"- RANSAC ground: {np.sum(vis_ransac_ground):,} points\")\n", "    print(f\"- Combined ground: {np.sum(vis_combined_ground):,} points\")\n", "    print(f\"- PMF improvement: +{np.sum(vis_combined_ground) - np.sum(vis_ransac_ground):,} points\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating 3D visualizations...\n", "- Original point cloud plot\n"]}, {"data": {"image/png": "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**********************************************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", "text/plain": ["<Figure size 2000x1500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create comprehensive comparison visualization\n", "if create_visualizations:\n", "    fig = plt.figure(figsize=(20, 15))\n", "    \n", "    # Calculate axis limits for consistent scaling\n", "    xlim = (vis_points[:, 0].min(), vis_points[:, 0].max())\n", "    ylim = (vis_points[:, 1].min(), vis_points[:, 1].max())\n", "    zlim = (vis_points[:, 2].min(), vis_points[:, 2].max())\n", "    \n", "    print(\"Creating 3D visualizations...\")\n", "    \n", "    # Plot 1: Original point cloud\n", "    ax1 = fig.add_subplot(2, 3, 1, projection='3d')\n", "    ax1.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2], \n", "                c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.6)\n", "    ax1.set_title('Original Point Cloud')\n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_zlabel('Z (m)')\n", "    ax1.set_xlim(xlim)\n", "    ax1.set_ylim(ylim)\n", "    ax1.set_zlim(zlim)\n", "    \n", "    print(\"- Original point cloud plot\")"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RANSAC results plot\n"]}], "source": ["# Plot 2: RANSAC results\n", "if create_visualizations:\n", "    ax2 = fig.add_subplot(2, 3, 2, projection='3d')\n", "    \n", "    # RANSAC ground points\n", "    ground_vis = vis_points[vis_ransac_ground]\n", "    nonground_vis = vis_points[~vis_ransac_ground]\n", "    \n", "    if len(ground_vis) > 0:\n", "        ax2.scatter(ground_vis[:, 0], ground_vis[:, 1], ground_vis[:, 2], \n", "                    c='brown', s=1, alpha=0.6, label='Ground')\n", "    if len(nonground_vis) > 0:\n", "        ax2.scatter(nonground_vis[:, 0], nonground_vis[:, 1], nonground_vis[:, 2], \n", "                    c='green', s=1, alpha=0.6, label='Non-ground')\n", "    \n", "    ax2.set_title('RANSAC Results')\n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Y (m)')\n", "    ax2.set_zlabel('Z (m)')\n", "    ax2.set_xlim(xlim)\n", "    ax2.set_ylim(ylim)\n", "    ax2.set_zlim(zlim)\n", "    ax2.legend()\n", "    \n", "    print(\"RANSAC results plot\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined results plot\n"]}], "source": ["# Plot 3: Combined RANSAC + PMF results\n", "if create_visualizations:\n", "    ax3 = fig.add_subplot(2, 3, 3, projection='3d')\n", "    \n", "    # Combined ground and non-ground points\n", "    combined_ground_vis = vis_points[vis_combined_ground]\n", "    combined_nonground_vis = vis_points[~vis_combined_ground]\n", "    \n", "    if len(combined_ground_vis) > 0:\n", "        ax3.scatter(combined_ground_vis[:, 0], combined_ground_vis[:, 1], combined_ground_vis[:, 2], \n", "                    c='brown', s=1, alpha=0.6, label='Ground')\n", "    if len(combined_nonground_vis) > 0:\n", "        ax3.scatter(combined_nonground_vis[:, 0], combined_nonground_vis[:, 1], combined_nonground_vis[:, 2], \n", "                    c='green', s=1, alpha=0.6, label='Non-ground')\n", "    \n", "    ax3.set_title('RANSAC + PMF Results')\n", "    ax3.set_xlabel('X (m)')\n", "    ax3.set_ylabel('Y (m)')\n", "    ax3.set_zlabel('Z (m)')\n", "    ax3.set_xlim(xlim)\n", "    ax3.set_ylim(ylim)\n", "    ax3.set_zlim(zlim)\n", "    ax3.legend()\n", "    \n", "    print(\"Combined results plot\")"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Height distribution plot\n"]}], "source": ["# Plot 4: Height distribution comparison\n", "if create_visualizations:\n", "    ax4 = fig.add_subplot(2, 3, 4)\n", "    bins = 50\n", "    \n", "    # Plot height histograms\n", "    ax4.hist(ransac_ground_points[:, 2], bins=bins, alpha=0.7, \n", "             label='RANSAC Ground', color='brown', edgecolor='black')\n", "    \n", "    if len(pmf_additional_ground_points) > 0:\n", "        ax4.hist(pmf_additional_ground_points[:, 2], bins=bins, alpha=0.7, \n", "                 label='PMF Additional', color='orange', edgecolor='black')\n", "    \n", "    ax4.hist(combined_nonground_points[:, 2], bins=bins, alpha=0.7, \n", "             label='Non-ground', color='green', edgecolor='black')\n", "    \n", "    ax4.set_title('Height Distribution')\n", "    ax4.set_xlabel('Z (meters)')\n", "    ax4.set_ylabel('Point Count')\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    print(\"Height distribution plot\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing time plot\n"]}], "source": ["# Plot 5: Processing time comparison\n", "if create_visualizations:\n", "    ax5 = fig.add_subplot(2, 3, 5)\n", "    \n", "    methods = ['RANSAC', 'PMF', 'Total']\n", "    times = [ransac_time, pmf_time, total_processing_time]\n", "    colors = ['blue', 'orange', 'red']\n", "    \n", "    bars = ax5.bar(methods, times, color=colors, alpha=0.7, edgecolor='black')\n", "    ax5.set_title('Processing Time Comparison')\n", "    ax5.set_ylabel('Time (seconds)')\n", "    \n", "    # Add value labels on bars\n", "    for bar, time_val in zip(bars, times):\n", "        ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(times)*0.01, \n", "                f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    ax5.grid(True, alpha=0.3)\n", "    \n", "    print(\"Processing time plot\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point count breakdown plot\n"]}], "source": ["# Plot 6: Point count breakdown\n", "if create_visualizations:\n", "    ax6 = fig.add_subplot(2, 3, 6)\n", "    \n", "    categories = ['RANSAC\\nGround', 'PMF\\nAdditional', 'Final\\nNon-ground']\n", "    counts = [len(ransac_ground_points), len(pmf_additional_ground_points), len(combined_nonground_points)]\n", "    colors = ['brown', 'orange', 'green']\n", "    \n", "    bars = ax6.bar(categories, counts, color=colors, alpha=0.7, edgecolor='black')\n", "    ax6.set_title('Point Count Breakdown')\n", "    ax6.set_ylabel('Point Count')\n", "    \n", "    # Add value labels on bars\n", "    for bar, count in zip(bars, counts):\n", "        ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01, \n", "                f'{count:,}', ha='center', va='bottom', fontweight='bold', rotation=0)\n", "    \n", "    ax6.grid(True, alpha=0.3)\n", "    \n", "    print(\"Point count breakdown plot\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Visualization saved to: output_runs/Castro_ransac_pmf_20250618_172002/ransac_pmf_comparison.png\n"]}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:26:08,807 - INFO - Visualization saved: output_runs/Castro_ransac_pmf_20250618_172002/ransac_pmf_comparison.png\n"]}], "source": ["# Finalize and save visualization\n", "if create_visualizations:\n", "    plt.tight_layout()\n", "    \n", "    # Save visualization\n", "    viz_file = current_run_path / \"ransac_pmf_comparison.png\"\n", "    plt.savefig(viz_file, dpi=300, bbox_inches='tight')\n", "    \n", "    print(f\"Visualization saved to: {viz_file}\")\n", "    \n", "    # Display the plot\n", "    plt.show()\n", "    \n", "    logger.info(f\"Visualization saved: {viz_file}\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PMF-specific visualization saved to: output_runs/Castro_ransac_pmf_20250618_172002/pmf_additional_ground.png\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Additional PMF-specific visualization (if PMF found additional points)\n", "if create_visualizations and len(pmf_additional_ground_points) > 0:\n", "    fig_pmf = plt.figure(figsize=(12, 8))\n", "    ax_pmf = fig_pmf.add_subplot(1, 1, 1, projection='3d')\n", "    \n", "    # Show only PMF additional ground points\n", "    ax_pmf.scatter(pmf_additional_ground_points[:, 0],\n", "                   pmf_additional_ground_points[:, 1],\n", "                   pmf_additional_ground_points[:, 2],\n", "                   c='orange', s=2, alpha=0.8, label='PMF Additional Ground')\n", "    \n", "    ax_pmf.set_title(f'PMF Recovered Ground Points ({len(pmf_additional_ground_points):,} points)')\n", "    ax_pmf.set_xlabel('X (m)')\n", "    ax_pmf.set_ylabel('Y (m)')\n", "    ax_pmf.set_zlabel('Z (m)')\n", "    ax_pmf.legend()\n", "    \n", "    # Save PMF-specific visualization\n", "    pmf_viz_file = current_run_path / \"pmf_additional_ground.png\"\n", "    plt.savefig(pmf_viz_file, dpi=300, bbox_inches='tight')\n", "    \n", "    print(f\"PMF-specific visualization saved to: {pmf_viz_file}\")\n", "    plt.show()\n", "    \n", "elif create_visualizations:\n", "    print(\"No PMF additional points to visualize\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Final Summary"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 17:26:53,408 - INFO - RANSAC+PMF processing completed successfully for Castro\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "======================================================================\n", "RANSAC + PMF GROUND SEGMENTATION COMPLETE\n", "======================================================================\n", "Site: Castro (ENEL)\n", "Method: ransac_pmf\n", "Timestamp: 20250618_172002\n", "\n", "Data Processing:\n", "- Input: 251,056,301 points\n", "- Processed: 1,000,000 points\n", "- Downsampling: Yes\n", "\n", "RANSAC Results:\n", "- Ground points: 487,633 (48.8%)\n", "- Processing time: 19.59 seconds\n", "- Plane equation: -0.003x + -0.004y + 1.000z + 19940.922 = 0\n", "\n", "PMF Results:\n", "- Additional ground: 91,818 (9.2%)\n", "- Processing time: 1.42 seconds\n", "- Recovery rate: 17.9% of non-ground points\n", "\n", "Final Results:\n", "- Total ground: 579,451 (57.9%)\n", "- Final non-ground: 420,549 (42.1%)\n", "- Improvement: ****% ground coverage\n", "- Total processing time: 21.00 seconds\n", "\n", "Outputs:\n", "- Main results: ../../../data/ENEL/Castro/ground_segmentation\n", "- Run details: output_runs/Castro_ransac_pmf_20250618_172002\n", "- Parameters: output_runs/Castro_ransac_pmf_20250618_172002/parameters.json\n", "- Results summary: output_runs/Castro_ransac_pmf_20250618_172002/results_summary.json\n", "  Visualization: output_runs/Castro_ransac_pmf_20250618_172002/ransac_pmf_comparison.png\n", "======================================================================\n"]}], "source": ["# Comprehensive final processing summary\n", "print(\"\\n\" + \"=\"*70)\n", "print(\"RANSAC + PMF GROUND SEGMENTATION COMPLETE\")\n", "print(\"=\"*70)\n", "print(f\"Site: {site_name} ({project_type})\")\n", "print(f\"Method: {method_name}\")\n", "print(f\"Timestamp: {timestamp}\")\n", "print(\"\")\n", "print(f\"Data Processing:\")\n", "print(f\"- Input: {original_point_count:,} points\")\n", "print(f\"- Processed: {len(points):,} points\")\n", "print(f\"- Downsampling: {'Yes' if len(points) < original_point_count else 'No'}\")\n", "print(\"\")\n", "print(f\"RANSAC Results:\")\n", "print(f\"- Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Processing time: {ransac_time:.2f} seconds\")\n", "print(f\"- Plane equation: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0\")\n", "print(\"\")\n", "print(f\"PMF Results:\")\n", "print(f\"- Additional ground: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Processing time: {pmf_time:.2f} seconds\")\n", "print(f\"- Recovery rate: {len(pmf_additional_ground_points)/len(ransac_nonground_points)*100:.1f}% of non-ground points\")\n", "print(\"\")\n", "print(f\"Final Results:\")\n", "print(f\"- Total ground: {len(combined_ground_points):,} ({len(combined_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)\")\n", "print(f\"- Improvement: +{len(pmf_additional_ground_points)/len(points)*100:.1f}% ground coverage\")\n", "print(f\"- Total processing time: {total_processing_time:.2f} seconds\")\n", "print(\"\")\n", "print(f\"Outputs:\")\n", "print(f\"- Main results: {ground_seg_path}\")\n", "print(f\"- Run details: {current_run_path}\")\n", "print(f\"- Parameters: {current_run_path / 'parameters.json'}\")\n", "print(f\"- Results summary: {current_run_path / 'results_summary.json'}\")\n", "if create_visualizations:\n", "    print(f\"  Visualization: {current_run_path / 'ransac_pmf_comparison.png'}\")\n", "print(\"=\"*70)\n", "\n", "logger.info(f\"RANSAC+PMF processing completed successfully for {site_name}\")"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}