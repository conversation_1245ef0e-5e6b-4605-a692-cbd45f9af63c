{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation: RANSAC + PMF Sequential Processing\n", "\n", "**Date**: June 2025  \n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "This notebook implements a sequential ground segmentation approach that combines RANSAC and Progressive Morphological Filter (PMF) methods:\n", "\n", "1. **RANSAC First**: Detects the primary ground plane using RANSAC\n", "2. **PMF Refinement**: Applies PMF to the non-ground points to recover additional ground points\n", "3. **Combined Results**: Merges both ground classifications for improved accuracy\n", "\n", "**Method**: RANSAC + PMF Sequential Processing  \n", "**Input Data**: Raw point cloud (.las, .laz files)  \n", "**Output**: Ground and non-ground point clouds with method-specific naming  \n", "**Format**: .ply files with ransac_pmf method identifier  \n", "\n", "## Workflow Steps\n", "\n", "1. **Setup & Configuration** - Parameters and paths\n", "2. **Data Loading** - Load LAS point cloud\n", "3. **RANSAC Ground Detection** - Find primary ground plane\n", "4. **PMF Refinement** - Process remaining points\n", "5. **Results Combination** - Merge ground classifications\n", "6. **Output Generation** - Save results and visualizations"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters cell\n", "# These parameters can be overridden when running with papermill\n", "\n", "# Site and project configuration\n", "site_name = \"Castro\"  # Site identifier\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# RANSAC parameters\n", "ransac_distance_threshold = 0.2  # Maximum distance for a point to be considered an inlier (meters)\n", "ransac_num_iterations = 1000     # Number of RANSAC iterations to perform\n", "ransac_min_inliers_ratio = 0.05  # Minimum ratio of inliers to accept a plane\n", "ransac_early_stop_ratio = 0.6    # Ratio of inliers to total points for early stopping\n", "\n", "# PMF parameters\n", "pmf_cell_size = 1.0  # Grid cell size for rasterization (meters)\n", "pmf_max_window_size = 33  # Maximum window size for morphological operations\n", "pmf_slope = 0.15  # Slope parameter for terrain (radians)\n", "pmf_max_distance = 2.5  # Maximum distance threshold (meters)\n", "pmf_initial_distance = 0.5  # Initial distance threshold (meters)\n", "\n", "# Processing parameters\n", "max_points_processing = 1000000  # Maximum points to process (for performance)\n", "visualization_enabled = True     # Enable 3D visualizations\n", "save_intermediate_results = True # Save intermediate point clouds\n", "\n", "# Output parameters\n", "save_ground_points = True        # Save ground points to PLY file\n", "save_nonground_points = True     # Save non-ground points to PLY file\n", "generate_summary_stats = True    # Generate summary statistics\n", "create_visualizations = True     # Create and save visualization plots"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup & Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "from scipy import ndimage\n", "from scipy.ndimage import grey_erosion, grey_dilation\n", "import open3d as o3d\n", "import laspy\n", "\n", "# Set random seed for reproducible results\n", "np.random.seed(42)\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "print(\"Logging configured\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup paths and directories\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "data_path = Path('../../..') / 'data'\n", "input_path = data_path / project_type / site_name / 'raw'\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "current_run_path = Path('output_runs') / f'{site_name}_ransac_pmf_{timestamp}'\n", "\n", "# Create output directories\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Input path: {input_path}\")\n", "print(f\"Output path: {ground_seg_path}\")\n", "print(f\"Run output: {current_run_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save parameters for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC+PMF Sequential\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": ransac_distance_threshold,\n", "        \"num_iterations\": ransac_num_iterations,\n", "        \"min_inliers_ratio\": ransac_min_inliers_ratio,\n", "        \"early_stop_ratio\": ransac_early_stop_ratio\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": pmf_cell_size,\n", "        \"max_window_size\": pmf_max_window_size,\n", "        \"slope\": pmf_slope,\n", "        \"max_distance\": pmf_max_distance,\n", "        \"initial_distance\": pmf_initial_distance\n", "    }\n", "}\n", "\n", "# Save to file\n", "with open(current_run_path / \"parameters.json\", 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {current_run_path / 'parameters.json'}\")\n", "logger.info(f\"RANSAC+PMF processing initialized for site: {site_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Discover LAS/LAZ files in input directory\n", "las_files = list(input_path.glob(\"*.las\")) + list(input_path.glob(\"*.laz\"))\n", "if not las_files:\n", "    raise FileNotFoundError(f\"No LAS/LAZ files found in {input_path}\")\n", "\n", "las_file = las_files[0]\n", "print(f\"Found {len(las_files)} LAS/LAZ file(s)\")\n", "print(f\"Loading: {las_file.name}\")\n", "logger.info(f\"Reading LAS file: {las_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load point cloud data from LAS file\n", "las = laspy.read(str(las_file))\n", "x, y, z = las.x, las.y, las.z\n", "points = np.column_stack((x, y, z))\n", "original_point_count = len(points)\n", "\n", "print(f\"Loaded {original_point_count:,} points\")\n", "print(f\"Bounds - X: {x.min():.2f} to {x.max():.2f}\")\n", "print(f\"       - Y: {y.min():.2f} to {y.max():.2f}\")\n", "print(f\"       - Z: {z.min():.2f} to {z.max():.2f}\")\n", "logger.info(f\"Loaded {original_point_count:,} points\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Downsample if necessary for processing efficiency\n", "if original_point_count > max_points_processing:\n", "    indices = np.random.choice(original_point_count, max_points_processing, replace=False)\n", "    points = points[indices]\n", "    print(f\"Downsampled to {len(points):,} points for processing\")\n", "    logger.info(f\"Downsampled from {original_point_count:,} to {len(points):,} points\")\n", "else:\n", "    print(f\"Using all {len(points):,} points for processing\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 16:45:40,480 - INFO - Checking base data path: ../../../data, Exists: True\n", "2025-06-18 16:45:40,481 - INFO - Created current run output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/preprocessing/ground_segmentation/output_runs/Castro_ransac_20250618_164540\n", "2025-06-18 16:45:40,482 - INFO - Using default input path: ../../../data/ENEL/Castro/raw\n", "2025-06-18 16:45:40,482 - INFO - Input path exists: True\n", "2025-06-18 16:45:40,482 - INFO - Ground segmentation output path exists: True\n"]}], "source": ["# Set up paths with proper project organization\n", "base_path = Path('../../..')\n", "data_path = base_path / 'data'\n", "logger.info(f\"Checking base data path: {data_path}, Exists: {data_path.exists()}\")\n", "\n", "# Create output directory structure for this run with method-specific naming\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Created current run output path: {current_run_path.resolve()}\")\n", "\n", "# Input and output paths following the specified organization\n", "if point_cloud_path:\n", "    input_path = Path(point_cloud_path)\n", "    logger.info(f\"Custom point_cloud_path provided: {input_path}\")\n", "    if not input_path.exists():\n", "        raise FileNotFoundError(f\"Input path does not exist: {input_path}\")\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "    logger.info(f\"Using default input path: {input_path}\")\n", "\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Input path exists: {input_path.exists()}\")\n", "logger.info(f\"Ground segmentation output path exists: {ground_seg_path.exists()}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters saved to: output_runs/Castro_ransac_20250618_164540/parameters.json\n", "\n", "Run Configuration:\n", "  RANSAC: threshold=0.2m, iterations=1000\n", "  PMF: cell_size=1.0m, max_window=33, slope=0.15\n"]}], "source": ["# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC+PMF Sequential\",\n", "        \"notebook_version\": \"1.0\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": ransac_distance_threshold,\n", "        \"num_iterations\": ransac_num_iterations,\n", "        \"min_inliers_ratio\": ransac_min_inliers_ratio,\n", "        \"early_stop_ratio\": ransac_early_stop_ratio\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": pmf_cell_size,\n", "        \"max_window_size\": pmf_max_window_size,\n", "        \"slope\": pmf_slope,\n", "        \"max_distance\": pmf_max_distance,\n", "        \"initial_distance\": pmf_initial_distance\n", "    },\n", "    \"processing_parameters\": {\n", "        \"max_points_processing\": max_points_processing,\n", "        \"buffer_radius\": buffer_radius,\n", "        \"visualization_enabled\": visualization_enabled,\n", "        \"save_intermediate_results\": save_intermediate_results\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(current_run_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = current_run_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {params_file}\")\n", "print(\"\\nRun Configuration:\")\n", "print(f\"  RANSAC: threshold={ransac_distance_threshold}m, iterations={ransac_num_iterations}\")\n", "print(f\"  PMF: cell_size={pmf_cell_size}m, max_window={pmf_max_window_size}, slope={pmf_slope}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading and Preprocessing\n", "\n", "Load point cloud data and prepare for processing."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 16:45:40,493 - INFO - Reading LAS file: ../../../data/ENEL/Castro/raw/area4_point.las\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Reading: ../../../data/ENEL/Castro/raw/area4_point.las\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-06-18 16:46:27,050 - INFO - Loaded 251,056,301 points.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 251,056,301 points\n", "Bounds - X: 707850.75 to 708080.57, Y: 4692889.07 to 4693090.37, Z: 51.06 to 68.48\n", "Downsampled to 1,000,000 points for processing\n"]}], "source": ["from pathlib import Path\n", "import numpy as np\n", "import laspy\n", "\n", "# Parameters\n", "max_points_processing = 1_000_000\n", "\n", "# Ensure input_path is defined and valid\n", "input_path = Path(input_path)\n", "if not input_path.exists() or not input_path.is_dir():\n", "    raise FileNotFoundError(f\"Invalid input_path: {input_path}\")\n", "\n", "# Discover LAS/LAZ files\n", "las_files = list(input_path.glob(\"*.las\")) + list(input_path.glob(\"*.laz\"))\n", "if not las_files:\n", "    raise FileNotFoundError(f\"No LAS/LAZ files found in {input_path}\")\n", "\n", "# Read first available LAS/LAZ file\n", "las_file = las_files[0]\n", "logger.info(f\"Reading LAS file: {las_file}\")\n", "print(f\"Reading: {las_file}\")\n", "\n", "las = laspy.read(str(las_file))\n", "x, y, z = las.x, las.y, las.z\n", "\n", "# Combine XYZ into Nx3 array\n", "points = np.column_stack((x, y, z))\n", "original_point_count = len(points)\n", "\n", "logger.info(f\"Loaded {original_point_count:,} points.\")\n", "print(f\"Loaded {original_point_count:,} points\")\n", "print(f\"Bounds - X: {x.min():.2f} to {x.max():.2f}, \"\n", "      f\"Y: {y.min():.2f} to {y.max():.2f}, \"\n", "      f\"Z: {z.min():.2f} to {z.max():.2f}\")\n", "\n", "# Downsample if necessary\n", "if original_point_count > max_points_processing:\n", "    indices = np.random.choice(original_point_count, max_points_processing, replace=False)\n", "    points = points[indices]\n", "    print(f\"Downsampled to {len(points):,} points for processing\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. RANSAC Ground Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize RANSAC parameters\n", "n_points = len(points)\n", "min_inliers = int(n_points * ransac_min_inliers_ratio)\n", "early_stop_inliers = int(n_points * ransac_early_stop_ratio)\n", "\n", "print(f\"RANSAC Configuration:\")\n", "print(f\"  Distance threshold: {ransac_distance_threshold}m\")\n", "print(f\"  Iterations: {ransac_num_iterations}\")\n", "print(f\"  Min inliers: {min_inliers:,} ({ransac_min_inliers_ratio*100:.1f}%)\")\n", "print(f\"  Early stop: {early_stop_inliers:,} ({ransac_early_stop_ratio*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RANSAC algorithm - find best ground plane\n", "best_plane_params = None\n", "best_inliers = []\n", "max_inliers = 0\n", "start_time = time.time()\n", "\n", "print(\"Running RANSAC iterations...\")\n", "for i in tqdm(range(ransac_num_iterations), desc=\"RANSAC\"):\n", "    # <PERSON><PERSON> 3 random points\n", "    sample_indices = np.random.choice(n_points, 3, replace=False)\n", "    p1, p2, p3 = points[sample_indices]\n", "    \n", "    # Compute plane normal from cross product\n", "    v1, v2 = p2 - p1, p3 - p1\n", "    normal = np.cross(v1, v2)\n", "    \n", "    # Skip degenerate cases\n", "    if np.linalg.norm(normal) < 1e-6:\n", "        continue\n", "    \n", "    # Normalize and ensure upward-facing\n", "    normal = normal / np.linalg.norm(normal)\n", "    if normal[2] < 0:\n", "        normal = -normal\n", "    \n", "    # Plane equation: ax + by + cz + d = 0\n", "    d = -np.dot(normal, p1)\n", "    plane_params = np.append(normal, d)\n", "    \n", "    # Calculate distances to plane\n", "    distances = np.abs(np.dot(points, plane_params[:3]) + d)\n", "    inliers = np.where(distances < ransac_distance_threshold)[0]\n", "    n_inliers = len(inliers)\n", "    \n", "    # Update best plane if better\n", "    if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "        best_plane_params = plane_params\n", "        best_inliers = inliers\n", "        max_inliers = n_inliers\n", "        \n", "        # Early stopping\n", "        if n_inliers >= early_stop_inliers:\n", "            print(f\"\\nEarly stopping at iteration {i+1} with {n_inliers:,} inliers\")\n", "            break\n", "\n", "ransac_time = time.time() - start_time\n", "\n", "if best_plane_params is None:\n", "    raise RuntimeError(\"RANSAC failed to find a valid ground plane\")\n", "\n", "print(f\"RANSAC completed in {ransac_time:.2f} seconds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create ground/non-ground point masks from RANSAC results\n", "ransac_ground_mask = np.zeros(len(points), dtype=bool)\n", "ransac_ground_mask[best_inliers] = True\n", "ransac_nonground_mask = ~ransac_ground_mask\n", "\n", "# Extract ground and non-ground points\n", "ransac_ground_points = points[ransac_ground_mask]\n", "ransac_nonground_points = points[ransac_nonground_mask]\n", "\n", "# Display RANSAC results\n", "print(f\"\\nRANSAC Results:\")\n", "print(f\"  Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"  Non-ground points: {len(ransac_nonground_points):,} ({len(ransac_nonground_points)/len(points)*100:.1f}%)\")\n", "print(f\"  Plane equation: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0\")\n", "\n", "logger.info(f\"RANSAC found {len(ransac_ground_points):,} ground points in {ransac_time:.2f}s\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: PMF Processing on Non-Ground Points\n", "\n", "Apply Progressive Morphological Filter to the non-ground points from RANSAC to recover additional ground points."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Applying PMF to 512,367 non-ground points from RANSAC...\n", "PMF Parameters: cell_size=1.0m, max_window=33, slope=0.15\n", "\n", "PMF Results:\n", "  Processing time: 1.08 seconds\n", "  Additional ground points found: 91,818\n", "  Remaining non-ground points: 420,549\n"]}], "source": ["def pmf_ground_detection(points, cell_size, max_window_size, slope):\n", "    \"\"\"Progressive Morphological Filter for ground detection.\"\"\"\n", "    if len(points) == 0:\n", "        return np.array([], dtype=bool)\n", "    \n", "    # Grid the point cloud (2D raster)\n", "    min_xy = np.min(points[:, :2], axis=0)\n", "    max_xy = np.max(points[:, :2], axis=0)\n", "    dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)\n", "    \n", "    if dims[0] <= 0 or dims[1] <= 0:\n", "        return np.array([False] * len(points))\n", "    \n", "    grid = np.full(dims, np.nan)\n", "    \n", "    # Populate raster with lowest Z value per cell\n", "    for i, (x, y, z) in enumerate(points):\n", "        xi = int((x - min_xy[0]) / cell_size)\n", "        yi = int((y - min_xy[1]) / cell_size)\n", "        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "            if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:\n", "                grid[xi, yi] = z\n", "    \n", "    # Fill holes\n", "    filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)\n", "    \n", "    # Morphological opening (erosion then dilation)\n", "    opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)\n", "    \n", "    # Ground mask based on slope threshold\n", "    z_diff = filled_grid - opened\n", "    ground_mask_2d = z_diff < slope\n", "    \n", "    # Reconstruct full ground point mask\n", "    ground_mask = []\n", "    for x, y, z in points:\n", "        xi = int((x - min_xy[0]) / cell_size)\n", "        yi = int((y - min_xy[1]) / cell_size)\n", "        if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "            ground_mask.append(ground_mask_2d[xi, yi])\n", "        else:\n", "            ground_mask.append(False)\n", "    \n", "    return np.array(ground_mask)\n", "\n", "# Apply PMF to non-ground points from RANSAC\n", "print(f\"\\nApplying PMF to {len(ransac_nonground_points):,} non-ground points from RANSAC...\")\n", "print(f\"PMF Parameters: cell_size={pmf_cell_size}m, max_window={pmf_max_window_size}, slope={pmf_slope}\")\n", "\n", "pmf_start_time = time.time()\n", "pmf_ground_mask_subset = pmf_ground_detection(\n", "    ransac_nonground_points, pmf_cell_size, pmf_max_window_size, pmf_slope\n", ")\n", "pmf_time = time.time() - pmf_start_time\n", "\n", "# Extract additional ground points found by PMF\n", "pmf_additional_ground_points = ransac_nonground_points[pmf_ground_mask_subset]\n", "pmf_remaining_nonground_points = ransac_nonground_points[~pmf_ground_mask_subset]\n", "\n", "print(f\"\\nPMF Results:\")\n", "print(f\"  Processing time: {pmf_time:.2f} seconds\")\n", "print(f\"  Additional ground points found: {len(pmf_additional_ground_points):,}\")\n", "print(f\"  Remaining non-ground points: {len(pmf_remaining_nonground_points):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Combine Results\n", "\n", "Merge <PERSON> and PMF results for final ground segmentation."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Combined Results (RANSAC + PMF):\n", "  Total processing time: 20.16 seconds\n", "  Final ground points: 579,451 (57.9%)\n", "  Final non-ground points: 420,549 (42.1%)\n", "  \n", "Breakdown:\n", "    RANSAC ground: 487,633 (48.8%)\n", "    PMF additional: 91,818 (9.2%)\n", "    Final non-ground: 420,549 (42.1%)\n", "\n", "Results summary saved to: output_runs/Castro_ransac_20250618_164540/results_summary.json\n"]}], "source": ["# Combine RANSAC ground points with PMF additional ground points\n", "combined_ground_points = np.vstack([\n", "    ransac_ground_points,\n", "    pmf_additional_ground_points\n", "])\n", "\n", "combined_nonground_points = pmf_remaining_nonground_points\n", "\n", "# Calculate final statistics\n", "total_processing_time = ransac_time + pmf_time\n", "final_ground_ratio = len(combined_ground_points) / len(points)\n", "final_nonground_ratio = len(combined_nonground_points) / len(points)\n", "\n", "print(f\"\\nCombined Results (RANSAC + PMF):\")\n", "print(f\"  Total processing time: {total_processing_time:.2f} seconds\")\n", "print(f\"  Final ground points: {len(combined_ground_points):,} ({final_ground_ratio*100:.1f}%)\")\n", "print(f\"  Final non-ground points: {len(combined_nonground_points):,} ({final_nonground_ratio*100:.1f}%)\")\n", "print(f\"  \\nBreakdown:\")\n", "print(f\"    RANSAC ground: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"    PMF additional: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"    Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)\")\n", "\n", "# Save results summary\n", "results_summary = {\n", "    \"processing_info\": {\n", "        \"total_points\": len(points),\n", "        \"original_points\": original_point_count,\n", "        \"processing_time_seconds\": total_processing_time,\n", "        \"ransac_time_seconds\": ransac_time,\n", "        \"pmf_time_seconds\": pmf_time\n", "    },\n", "    \"ransac_results\": {\n", "        \"ground_points\": len(ransac_ground_points),\n", "        \"ground_ratio\": len(ransac_ground_points) / len(points),\n", "        \"plane_equation\": plane_params.tolist() if plane_params is not None else None\n", "    },\n", "    \"pmf_results\": {\n", "        \"additional_ground_points\": len(pmf_additional_ground_points),\n", "        \"additional_ground_ratio\": len(pmf_additional_ground_points) / len(points)\n", "    },\n", "    \"combined_results\": {\n", "        \"final_ground_points\": len(combined_ground_points),\n", "        \"final_nonground_points\": len(combined_nonground_points),\n", "        \"final_ground_ratio\": final_ground_ratio,\n", "        \"final_nonground_ratio\": final_nonground_ratio\n", "    }\n", "}\n", "\n", "# Save results to JSON\n", "results_file = current_run_path / \"results_summary.json\"\n", "with open(results_file, 'w') as f:\n", "    json.dump(results_summary, f, indent=2)\n", "\n", "print(f\"\\nResults summary saved to: {results_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Save Results\n", "\n", "Save the segmented point clouds and processing results."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-18 16:48:11,740 - INFO - Saved: ../../../data/ENEL/Castro/ground_segmentation/Castro_ransac_pmf_ground.ply\n", "2025-06-18 16:48:11,772 - INFO - Saved: output_runs/Castro_ransac_20250618_164540/ground_points.ply\n", "2025-06-18 16:48:11,790 - INFO - Saved: ../../../data/ENEL/Castro/ground_segmentation/Castro_ransac_pmf_nonground.ply\n", "2025-06-18 16:48:11,814 - INFO - Saved: output_runs/Castro_ransac_20250618_164540/nonground_points.ply\n", "2025-06-18 16:48:11,840 - INFO - Saved: output_runs/Castro_ransac_20250618_164540/ransac_only_ground.ply\n", "2025-06-18 16:48:11,867 - INFO - Saved: output_runs/Castro_ransac_20250618_164540/ransac_only_nonground.ply\n", "2025-06-18 16:48:11,873 - INFO - Saved: output_runs/Castro_ransac_20250618_164540/pmf_additional_ground.ply\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Intermediate results saved to: output_runs/Castro_ransac_20250618_164540\n", "\n", "RANSAC+PMF segmentation outputs saved:\n", "  Combined ground points: 579,451\n", "  Combined non-ground points: 420,549\n", "  Method identifier: ransac_pmf\n", "  All results saved successfully!\n"]}], "source": ["def save_points_to_ply(points, filename, colors=None):\n", "    \"\"\"Save points to PLY file with optional colors.\"\"\"\n", "    with open(filename, 'w') as f:\n", "        # Write header\n", "        f.write(\"ply\\n\")\n", "        f.write(\"format ascii 1.0\\n\")\n", "        f.write(f\"element vertex {len(points)}\\n\")\n", "        f.write(\"property float x\\n\")\n", "        f.write(\"property float y\\n\")\n", "        f.write(\"property float z\\n\")\n", "        if colors is not None:\n", "            f.write(\"property uchar red\\n\")\n", "            f.write(\"property uchar green\\n\")\n", "            f.write(\"property uchar blue\\n\")\n", "        f.write(\"end_header\\n\")\n", "        \n", "        # Write points\n", "        for i, point in enumerate(points):\n", "            if colors is not None:\n", "                f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {colors[i][0]} {colors[i][1]} {colors[i][2]}\\n\")\n", "            else:\n", "                f.write(f\"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f}\\n\")\n", "\n", "# Save segmented point clouds using Open3D (consistent with other notebooks)\n", "import open3d as o3d\n", "\n", "def save_ply(path, points_array, method_name=None):\n", "    \"\"\"Save points to PLY file using Open3D (consistent with other ground segmentation notebooks).\"\"\"\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save ground and non-ground points with method-specific naming\n", "method_name = \"ransac_pmf\"\n", "\n", "if save_ground_points:\n", "    ground_file = ground_seg_path / f\"{site_name}_{method_name}_ground.ply\"\n", "    save_ply(ground_file, combined_ground_points, \"combined ground\")\n", "    \n", "    # Also save to run directory\n", "    run_ground_file = current_run_path / \"ground_points.ply\"\n", "    save_ply(run_ground_file, combined_ground_points)\n", "\n", "if save_nonground_points:\n", "    nonground_file = ground_seg_path / f\"{site_name}_{method_name}_nonground.ply\"\n", "    save_ply(nonground_file, combined_nonground_points, \"combined non-ground\")\n", "    \n", "    # Also save to run directory\n", "    run_nonground_file = current_run_path / \"nonground_points.ply\"\n", "    save_ply(run_nonground_file, combined_nonground_points)\n", "\n", "# Save intermediate results if requested\n", "if save_intermediate_results:\n", "    # RANSAC-only results\n", "    ransac_ground_file = current_run_path / \"ransac_only_ground.ply\"\n", "    ransac_nonground_file = current_run_path / \"ransac_only_nonground.ply\"\n", "    save_ply(ransac_ground_file, ransac_ground_points, \"RANSAC ground\")\n", "    save_ply(ransac_nonground_file, ransac_nonground_points, \"RANSAC non-ground\")\n", "    \n", "    # PMF additional results\n", "    if len(pmf_additional_ground_points) > 0:\n", "        pmf_additional_file = current_run_path / \"pmf_additional_ground.ply\"\n", "        save_ply(pmf_additional_file, pmf_additional_ground_points, \"PMF additional ground\")\n", "    \n", "    print(f\"Intermediate results saved to: {current_run_path}\")\n", "\n", "print(f\"\\nRANSAC+PMF segmentation outputs saved:\")\n", "print(f\"  Combined ground points: {len(combined_ground_points):,}\")\n", "print(f\"  Combined non-ground points: {len(combined_nonground_points):,}\")\n", "print(f\"  Method identifier: {method_name}\")\n", "print(f\"  All results saved successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Visualization and Analysis\n", "\n", "Create visualizations to compare the different segmentation results."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prepare Point Cloud Data for Visualization\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Subsampling and Ground Mask Preparation\n", "# Parameters\n", "vis_sample_size = 50000\n", "tolerance = 0.1  # distance threshold for PMF match\n", "\n", "if len(points) > vis_sample_size:\n", "    vis_indices = np.random.choice(len(points), vis_sample_size, replace=False)\n", "    vis_points = points[vis_indices]\n", "    vis_ransac_ground = ransac_ground_mask[vis_indices]\n", "else:\n", "    vis_points = points\n", "    vis_ransac_ground = ransac_ground_mask"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'Z')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Original point cloud\n", "fig = plt.figure(figsize=(20, 15))\n", "\n", "ax1 = fig.add_subplot(2, 3, 1, projection='3d')\n", "ax1.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2],\n", "            c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.6)\n", "ax1.set_title('Original Point Cloud')\n", "ax1.set_xlabel('X')\n", "ax1.set_ylabel('Y')\n", "ax1.set_zlabel('Z')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Create combined ground mask\n", "vis_combined_ground = np.zeros(len(vis_points), dtype=bool)\n", "vis_combined_ground[vis_ransac_ground] = True\n", "\n", "for i, point in enumerate(vis_points):\n", "    if not vis_ransac_ground[i]:\n", "        distances = np.linalg.norm(pmf_additional_ground_points - point, axis=1)\n", "        if len(distances) > 0 and np.min(distances) < tolerance:\n", "            vis_combined_ground[i] = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize Figure"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 2200x1400 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig = plt.figure(figsize=(22, 14))\n", "\n", "# Axis limits\n", "xlim = (np.min(vis_points[:, 0]), np.max(vis_points[:, 0]))\n", "ylim = (np.min(vis_points[:, 1]), np.max(vis_points[:, 1]))\n", "zlim = (np.min(vis_points[:, 2]), np.max(vis_points[:, 2]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot 1: Original Point Cloud"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["(51.196, 63.341)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ax1 = fig.add_subplot(2, 3, 1, projection='3d')\n", "ax1.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2],\n", "            c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.5)\n", "ax1.set_title('Original Point Cloud')\n", "ax1.set_xlabel('X')\n", "ax1.set_ylabel('Y')\n", "ax1.set_zlabel('Z')\n", "ax1.view_init(elev=20, azim=60)\n", "ax1.set_xlim(xlim)\n", "ax1.set_ylim(ylim)\n", "ax1.set_zlim(zlim)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot 2: RANSAC Results"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["(51.196, 63.341)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ax2 = fig.add_subplot(2, 3, 2, projection='3d')\n", "ax2.scatter(vis_points[vis_ransac_ground][:, 0],\n", "            vis_points[vis_ransac_ground][:, 1],\n", "            vis_points[vis_ransac_ground][:, 2],\n", "            c='brown', s=1, alpha=0.6, label='Ground')\n", "ax2.scatter(vis_points[~vis_ransac_ground][:, 0],\n", "            vis_points[~vis_ransac_ground][:, 1],\n", "            vis_points[~vis_ransac_ground][:, 2],\n", "            c='green', s=1, alpha=0.6, label='Non-ground')\n", "ax2.set_title('RANSAC Results')\n", "ax2.set_xlabel('X')\n", "ax2.set_ylabel('Y')\n", "ax2.set_zlabel('Z')\n", "ax2.view_init(elev=20, azim=60)\n", "ax2.legend(title=\"Legend\", title_fontsize='small')\n", "ax2.set_xlim(xlim)\n", "ax2.set_ylim(ylim)\n", "ax2.set_zlim(zlim)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot 3: Combined RANSAC + PMF Results\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["(51.196, 63.341)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ax3 = fig.add_subplot(2, 3, 3, projection='3d')\n", "ax3.scatter(vis_points[vis_combined_ground][:, 0],\n", "            vis_points[vis_combined_ground][:, 1],\n", "            vis_points[vis_combined_ground][:, 2],\n", "            c='brown', s=1, alpha=0.6, label='Ground')\n", "ax3.scatter(vis_points[~vis_combined_ground][:, 0],\n", "            vis_points[~vis_combined_ground][:, 1],\n", "            vis_points[~vis_combined_ground][:, 2],\n", "            c='green', s=1, alpha=0.6, label='Non-ground')\n", "ax3.set_title('RANSAC + PMF Results')\n", "ax3.set_xlabel('X')\n", "ax3.set_ylabel('Y')\n", "ax3.set_zlabel('Z')\n", "ax3.view_init(elev=20, azim=60)\n", "ax3.legend(title=\"Legend\", title_fontsize='small')\n", "ax3.set_xlim(xlim)\n", "ax3.set_ylim(ylim)\n", "ax3.set_zlim(zlim)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot 4: Height Distribution"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x17680f640>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["ax4 = fig.add_subplot(2, 3, 4)\n", "bins = 30\n", "ax4.hist(ransac_ground_points[:, 2], bins=bins, alpha=0.6, label='RANSAC Ground', color='brown', edgecolor='black')\n", "if len(pmf_additional_ground_points) > 0:\n", "    ax4.hist(pmf_additional_ground_points[:, 2], bins=bins, alpha=0.6, label='PMF Additional', color='orange', edgecolor='black')\n", "ax4.hist(combined_nonground_points[:, 2], bins=bins, alpha=0.6, label='Non-ground', color='green', edgecolor='black')\n", "ax4.set_title('Height Distribution')\n", "ax4.set_xlabel('Z (meters)')\n", "ax4.set_ylabel('Point Count')\n", "ax4.grid(True, alpha=0.3)\n", "ax4.legend()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot 5: Processing Time Comparison"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["ax5 = fig.add_subplot(2, 3, 5)\n", "methods = ['RANSAC', 'PMF', 'Total']\n", "times = [ransac_time, pmf_time, total_processing_time]\n", "bars = ax5.bar(methods, times, color=['blue', 'orange', 'red'])\n", "ax5.set_title('Processing Time Comparison')\n", "ax5.set_ylabel('Time (seconds)')\n", "for bar, time_val in zip(bars, times):\n", "    ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,\n", "             f'{time_val:.2f}s', ha='center', va='bottom')\n", "ax5.grid(True, alpha=0.3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot 6: Point Count Breakdown"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["ax6 = fig.add_subplot(2, 3, 6)\n", "categories = ['RANSAC\\nGround', 'PMF\\nAdditional', 'Non-ground']\n", "counts = [len(ransac_ground_points), len(pmf_additional_ground_points), len(combined_nonground_points)]\n", "colors = ['brown', 'orange', 'green']\n", "bars = ax6.bar(categories, counts, color=colors)\n", "ax6.set_title('Point Count Breakdown')\n", "ax6.set_ylabel('Point Count')\n", "for bar, count in zip(bars, counts):\n", "    ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01,\n", "             f'{count:,}', ha='center', va='bottom')\n", "ax6.grid(True, alpha=0.3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Save and Display Plot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Visualization saved to: output_runs/Castro_ransac_20250618_164540/ransac_pmf_comparison.png\n"]}], "source": ["plt.tight_layout()\n", "viz_file = current_run_path / \"ransac_pmf_comparison.png\"\n", "plt.savefig(viz_file, dpi=300, bbox_inches='tight')\n", "plt.show()\n", "print(f\"Visualization saved to: {viz_file}\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x176db62e0>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["ax_extra = fig.add_subplot(1, 1, 1, projection='3d')\n", "ax_extra.scatter(pmf_additional_ground_points[:, 0],\n", "                 pmf_additional_ground_points[:, 1],\n", "                 pmf_additional_ground_points[:, 2],\n", "                 c='orange', s=1, label='PMF Additional (Edges)')\n", "ax_extra.set_title(\"PMF Recovered Ground Points\")\n", "ax_extra.legend()"]}], "metadata": {"kernelspec": {"display_name": "sam_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}