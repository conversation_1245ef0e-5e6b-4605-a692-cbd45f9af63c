# Papermill parameters - can be overridden during execution
site_name = "site_001"
ground_method = "csf"  # Options: csf, pmf, ransac
confidence_threshold = 0.7
model_path = "../../models/pointnet_plus_plus_isection_pile.pth"
input_data_dir = "../../data/processed/ground_segmentation"
output_dir = "../../output_runs/pile_detection"
mlflow_experiment_name = "pile_detection_pointnet_plus_plus"
mlflow_run_name = f"i_section_{site_name}_{ground_method}"
enable_cross_validation = True
cv_folds = 5
enable_enhanced_analysis = True

# Install required packages
!pip install torch torchvision torch-geometric torch-points3d open3d matplotlib numpy scipy pandas
!pip install scikit-learn plotly mlflow

# Import libraries
import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import open3d as o3d
from sklearn.neighbors import NearestNeighbors
from sklearn.cluster import DBSCAN
from scipy.spatial import ConvexHull
from scipy.spatial.distance import cdist

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch_geometric.nn import PointConv, global_max_pool, global_mean_pool
from torch_geometric.data import Data, Batch
from torch_geometric.nn import knn_graph

import warnings
warnings.filterwarnings('ignore')

print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Enhanced analysis and tracking imports
import pandas as pd
import json
import time
from datetime import datetime
from sklearn.model_selection import KFold, cross_val_score
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, roc_curve, auc
from sklearn.metrics import precision_recall_curve, average_precision_score
import seaborn as sns
from scipy import stats

# MLflow tracking
try:
    import mlflow
    import mlflow.pytorch
    import mlflow.sklearn
    MLFLOW_AVAILABLE = True
    print("MLflow available for experiment tracking")
except ImportError:
    MLFLOW_AVAILABLE = False
    print("MLflow not available - install with: pip install mlflow")

# Create output directories
output_dir = Path(output_dir)
output_dir.mkdir(parents=True, exist_ok=True)

# Initialize MLflow if available
if MLFLOW_AVAILABLE:
    mlflow.set_experiment(mlflow_experiment_name)
    mlflow.start_run(run_name=mlflow_run_name)
    
    # Log parameters
    mlflow.log_param("site_name", site_name)
    mlflow.log_param("ground_method", ground_method)
    mlflow.log_param("confidence_threshold", confidence_threshold)
    mlflow.log_param("model_architecture", "PointNet++")
    mlflow.log_param("pile_type", "i_section")
    mlflow.log_param("enable_cross_validation", enable_cross_validation)
    mlflow.log_param("cv_folds", cv_folds)

print(f"Processing site: {site_name}")
print(f"Ground segmentation method: {ground_method}")
print(f"Confidence threshold: {confidence_threshold}")
print(f"Cross-validation enabled: {enable_cross_validation}")
print(f"Enhanced analysis enabled: {enable_enhanced_analysis}")

class ISectionPileConfig:
    """
    Configuration for I-section pile detection using PointNet++.
    """
    def __init__(self):
        # I-section dimensions (typical ranges)
        #self.flange_width_range = (0.1, 0.5)  # meters
        #self.web_height_range = (0.2, 1.2)  # meters
        #self.flange_thickness_range = (0.01, 0.05)  # meters
        #self.web_thickness_range = (0.008, 0.03)  # meters
        self.height_range = (0.2, 1.5)  # meters

        self.flange_width_range = (0.08, 0.6)  # Slightly expanded
        self.web_height_range = (0.15, 1.5)   # Extended for larger sections
        self.flange_thickness_range = (0.008, 0.06)  # More comprehensive
        self.web_thickness_range = (0.006, 0.04)     # Include thinner webs

        # Detection parameters
        #self.patch_size = 2.0  # meters
        #self.min_points_per_patch = 100
        self.overlap_ratio = 0.5

        # Detection parameters - optimized for structural scale
        self.patch_size = 3.0  # Larger patches for building-scale detection
        self.min_points_per_patch = 150  # More points for better geometry

        # Add geometric validation
        self.min_aspect_ratio = 2.0  # web_height/flange_width
        self.max_aspect_ratio = 15.0

        # PointNet++ parameters
        self.num_points = 1024
        self.num_classes = 2  # pile vs non-pile
    
        # Context-aware detection
        self.connection_detection = True  # Detect beam-column connections
        self.orientation_tolerance = 15.0  # degrees

        # Set abstraction parameters
        self.sa_npoints = [512, 128, None]  # Number of points in each SA layer
        self.sa_radius = [0.2, 0.4, None]   # Radius for each SA layer
        self.sa_nsample = [64, 64, None]    # Number of samples in each SA layer
        self.sa_mlps = [[64, 64, 128], [128, 128, 256], [256, 512, 1024]]  # MLP dimensions
        
        # Feature propagation parameters
        self.fp_mlps = [[256, 256], [256, 128], [128, 128, 128]]  # FP MLP dimensions

config = ISectionPileConfig()
print("I-Section Pile Detection Configuration (PointNet++):")
print(f"Flange width range: {config.flange_width_range} m")
print(f"Web height range: {config.web_height_range} m")
print(f"Flange thickness range: {config.flange_thickness_range} m")
print(f"Web thickness range: {config.web_thickness_range} m")
print(f"Patch size: {config.patch_size} m")
print(f"Set abstraction layers: {len(config.sa_npoints)}")
print(f"Feature propagation layers: {len(config.fp_mlps)}")

def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):
    """
    Generate overlapping patches from a point cloud.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud coordinates (N, 3)
    colors : numpy.ndarray, optional
        RGB colors (N, 3)
    patch_size : float
        Size of each patch in meters
    overlap_ratio : float
        Overlap ratio between adjacent patches
    min_points : int
        Minimum number of points per patch
        
    Returns:
    --------
    patches : list
        List of patch dictionaries containing points, colors, and metadata
    """
    # Calculate bounds
    min_coords = points.min(axis=0)
    max_coords = points.max(axis=0)
    
    # Calculate step size
    step_size = patch_size * (1 - overlap_ratio)
    
    patches = []
    patch_id = 0
    
    # Generate grid of patch centers
    x_centers = np.arange(min_coords[0], max_coords[0], step_size)
    y_centers = np.arange(min_coords[1], max_coords[1], step_size)
    
    for x_center in x_centers:
        for y_center in y_centers:
            # Define patch bounds
            x_min = x_center - patch_size / 2
            x_max = x_center + patch_size / 2
            y_min = y_center - patch_size / 2
            y_max = y_center + patch_size / 2
            
            # Find points within patch
            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &
                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))
            
            patch_points = points[mask]
            
            if len(patch_points) >= min_points:
                # Center the patch points
                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])
                centered_points = patch_points - patch_center
                
                patch_data = {
                    'id': patch_id,
                    'points': centered_points,
                    'original_points': patch_points,
                    'center': patch_center,
                    'bounds': (x_min, y_min, x_max, y_max),
                    'num_points': len(patch_points)
                }
                
                if colors is not None:
                    patch_data['colors'] = colors[mask]
                
                patches.append(patch_data)
                patch_id += 1
    
    print(f"Generated {len(patches)} patches from point cloud")
    return patches

def analyze_i_section_geometry(points, tolerance=0.05):
    """
    Analyze point cloud patch for I-section pile geometry.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud patch (N, 3)
    tolerance : float
        Tolerance for geometric analysis
        
    Returns:
    --------
    features : dict
        Geometric features indicating I-section characteristics
    """
    if len(points) < 10:
        return None
    
    # Project points to XY plane for cross-section analysis
    xy_points = points[:, :2]
    
    # Find convex hull
    try:
        hull = ConvexHull(xy_points)
        hull_points = xy_points[hull.vertices]
    except:
        return None
    
    # Calculate bounding box
    min_coords = points.min(axis=0)
    max_coords = points.max(axis=0)
    dimensions = max_coords - min_coords
    
    # Analyze cross-sectional shape for I-section
    # For I-section, we expect:
    # 1. Two horizontal flanges connected by a vertical web
    # 2. Symmetric about both horizontal and vertical axes
    # 3. Specific width-to-height ratios
    # 4. Hollow regions between flanges
    
    # Calculate aspect ratios
    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0
    if xy_aspect < 1:
        xy_aspect = 1 / xy_aspect
    
    # Analyze point distribution for I-section pattern
    # Cluster points in cross-section
    clustering = DBSCAN(eps=tolerance, min_samples=5)
    cluster_labels = clustering.fit_predict(xy_points)
    
    unique_labels = np.unique(cluster_labels)
    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)
    
    # Calculate symmetry measures
    center_x, center_y = xy_points.mean(axis=0)
    
    # Check for horizontal symmetry (top and bottom flanges)
    upper_points = xy_points[xy_points[:, 1] > center_y]
    lower_points = xy_points[xy_points[:, 1] < center_y]
    
    # Check for vertical symmetry (left and right sides)
    left_points = xy_points[xy_points[:, 0] < center_x]
    right_points = xy_points[xy_points[:, 0] > center_x]
    
    # Calculate symmetry scores
    h_symmetry = min(len(upper_points), len(lower_points)) / max(len(upper_points), len(lower_points), 1)
    v_symmetry = min(len(left_points), len(right_points)) / max(len(left_points), len(right_points), 1)
    
    # Analyze density distribution for I-shape detection
    # Divide into grid to detect hollow center characteristic of I-sections
    grid_size = 5
    x_bins = np.linspace(xy_points[:, 0].min(), xy_points[:, 0].max(), grid_size)
    y_bins = np.linspace(xy_points[:, 1].min(), xy_points[:, 1].max(), grid_size)
    
    density_grid = np.zeros((grid_size-1, grid_size-1))
    for i in range(grid_size-1):
        for j in range(grid_size-1):
            mask = ((xy_points[:, 0] >= x_bins[i]) & (xy_points[:, 0] < x_bins[i+1]) &
                   (xy_points[:, 1] >= y_bins[j]) & (xy_points[:, 1] < y_bins[j+1]))
            density_grid[i, j] = mask.sum()
    
    # I-section should have high density at edges (flanges) and low density in center
    edge_density = (density_grid[0, :].mean() + density_grid[-1, :].mean() + 
                   density_grid[:, 0].mean() + density_grid[:, -1].mean()) / 4
    center_density = density_grid[grid_size//2-1:grid_size//2+1, grid_size//2-1:grid_size//2+1].mean()
    
    hollow_ratio = 1 - (center_density / (edge_density + 1e-6))
    
    # Calculate features
    features = {
        'num_points': len(points),
        'dimensions': dimensions,
        'xy_aspect_ratio': xy_aspect,
        'height': dimensions[2],
        'width': max(dimensions[0], dimensions[1]),
        'thickness': min(dimensions[0], dimensions[1]),
        'num_clusters': num_clusters,
        'hull_area': hull.volume if hasattr(hull, 'volume') else 0,
        'h_symmetry': h_symmetry,
        'v_symmetry': v_symmetry,
        'hollow_ratio': hollow_ratio,
        'edge_density': edge_density,
        'center_density': center_density,
        'compactness': len(points) / (hull.volume + 1e-6) if hasattr(hull, 'volume') else 0,
        'i_section_score': h_symmetry * v_symmetry * hollow_ratio
    }
    
    return features

class SetAbstractionLayer(nn.Module):
    """
    Set Abstraction layer for PointNet++.
    """
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all=False):
        super(SetAbstractionLayer, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.group_all = group_all
        
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        
        last_channel = in_channel + 3  # +3 for xyz coordinates
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz, points):
        """
        Parameters:
        -----------
        xyz : torch.Tensor
            Point coordinates (B, N, 3)
        points : torch.Tensor
            Point features (B, N, C) or None
            
        Returns:
        --------
        new_xyz : torch.Tensor
            Sampled point coordinates (B, npoint, 3)
        new_points : torch.Tensor
            Aggregated point features (B, npoint, mlp[-1])
        """
        B, N, C = xyz.shape
        
        if self.group_all:
            # Global pooling
            new_xyz = xyz.mean(dim=1, keepdim=True)  # (B, 1, 3)
            if points is not None:
                new_points = torch.cat([xyz, points], dim=-1)  # (B, N, 3+C)
            else:
                new_points = xyz  # (B, N, 3)
            new_points = new_points.unsqueeze(2)  # (B, N, 1, 3+C)
        else:
            # Farthest point sampling
            fps_idx = self.farthest_point_sample(xyz, self.npoint)
            new_xyz = self.index_points(xyz, fps_idx)  # (B, npoint, 3)
            
            # Ball query
            idx = self.ball_query(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = self.index_points(xyz, idx)  # (B, npoint, nsample, 3)
            grouped_xyz_norm = grouped_xyz - new_xyz.unsqueeze(2)  # Normalize
            
            if points is not None:
                grouped_points = self.index_points(points, idx)  # (B, npoint, nsample, C)
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # (B, npoint, nsample, 3+C)
            else:
                new_points = grouped_xyz_norm  # (B, npoint, nsample, 3)
        
        # Apply MLPs
        new_points = new_points.permute(0, 3, 2, 1)  # (B, 3+C, nsample, npoint)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))
        
        # Max pooling
        new_points = torch.max(new_points, 2)[0]  # (B, mlp[-1], npoint)
        new_points = new_points.permute(0, 2, 1)  # (B, npoint, mlp[-1])
        
        return new_xyz, new_points
    
    def farthest_point_sample(self, xyz, npoint):
        """
        Farthest point sampling.
        """
        device = xyz.device
        B, N, C = xyz.shape
        centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
        distance = torch.ones(B, N).to(device) * 1e10
        farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
        batch_indices = torch.arange(B, dtype=torch.long).to(device)
        
        for i in range(npoint):
            centroids[:, i] = farthest
            centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
            dist = torch.sum((xyz - centroid) ** 2, -1)
            mask = dist < distance
            distance[mask] = dist[mask]
            farthest = torch.max(distance, -1)[1]
        
        return centroids
    
    def ball_query(self, radius, nsample, xyz, new_xyz):
        """
        Ball query for grouping points.
        """
        device = xyz.device
        B, N, C = xyz.shape
        _, S, _ = new_xyz.shape
        group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
        
        sqrdists = self.square_distance(new_xyz, xyz)
        group_idx[sqrdists > radius ** 2] = N
        group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
        group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
        mask = group_idx == N
        group_idx[mask] = group_first[mask]
        
        return group_idx
    
    def square_distance(self, src, dst):
        """
        Calculate squared distance between points.
        """
        B, N, _ = src.shape
        _, M, _ = dst.shape
        dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
        dist += torch.sum(src ** 2, -1).view(B, N, 1)
        dist += torch.sum(dst ** 2, -1).view(B, 1, M)
        return dist
    
    def index_points(self, points, idx):
        """
        Index points using indices.
        """
        device = points.device
        B = points.shape[0]
        view_shape = list(idx.shape)
        view_shape[1:] = [1] * (len(view_shape) - 1)
        repeat_shape = list(idx.shape)
        repeat_shape[0] = 1
        batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)
        new_points = points[batch_indices, idx, :]
        return new_points

class FeaturePropagationLayer(nn.Module):
    """
    Feature Propagation layer for PointNet++.
    """
    def __init__(self, in_channel, mlp):
        super(FeaturePropagationLayer, self).__init__()
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm1d(out_channel))
            last_channel = out_channel
    
    def forward(self, xyz1, xyz2, points1, points2):
        """
        Parameters:
        -----------
        xyz1 : torch.Tensor
            Coordinates of points to interpolate to (B, N, 3)
        xyz2 : torch.Tensor
            Coordinates of points with features (B, M, 3)
        points1 : torch.Tensor
            Features of points1 (B, N, C1) or None
        points2 : torch.Tensor
            Features of points2 (B, M, C2)
            
        Returns:
        --------
        new_points : torch.Tensor
            Interpolated features (B, N, mlp[-1])
        """
        B, N, C = xyz1.shape
        _, M, _ = xyz2.shape
        
        if M == 1:
            # Global feature, repeat for all points
            interpolated_points = points2.repeat(1, N, 1)
        else:
            # Interpolate using inverse distance weighting
            dists = self.square_distance(xyz1, xyz2)
            dists, idx = dists.sort(dim=-1)
            dists, idx = dists[:, :, :3], idx[:, :, :3]  # Use 3 nearest neighbors
            
            dist_recip = 1.0 / (dists + 1e-8)
            norm = torch.sum(dist_recip, dim=2, keepdim=True)
            weight = dist_recip / norm
            
            interpolated_points = torch.sum(self.index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)
        
        if points1 is not None:
            new_points = torch.cat([points1, interpolated_points], dim=-1)
        else:
            new_points = interpolated_points
        
        # Apply MLPs
        new_points = new_points.permute(0, 2, 1)  # (B, C, N)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))
        
        new_points = new_points.permute(0, 2, 1)  # (B, N, C)
        return new_points
    
    def square_distance(self, src, dst):
        """
        Calculate squared distance between points.
        """
        B, N, _ = src.shape
        _, M, _ = dst.shape
        dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
        dist += torch.sum(src ** 2, -1).view(B, N, 1)
        dist += torch.sum(dst ** 2, -1).view(B, 1, M)
        return dist
    
    def index_points(self, points, idx):
        """
        Index points using indices.
        """
        device = points.device
        B = points.shape[0]
        view_shape = list(idx.shape)
        view_shape[1:] = [1] * (len(view_shape) - 1)
        repeat_shape = list(idx.shape)
        repeat_shape[0] = 1
        batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)
        new_points = points[batch_indices, idx, :]
        return new_points

class PointNetPlusPlusISectionPile(nn.Module):
    """
    PointNet++ model for I-section pile detection.
    """
    def __init__(self, num_classes=2, dropout=0.5):
        super(PointNetPlusPlusISectionPile, self).__init__()
        self.num_classes = num_classes
        
        # Set Abstraction layers
        self.sa1 = SetAbstractionLayer(
            npoint=config.sa_npoints[0], 
            radius=config.sa_radius[0], 
            nsample=config.sa_nsample[0],
            in_channel=0,  # No input features, only xyz
            mlp=config.sa_mlps[0]
        )
        
        self.sa2 = SetAbstractionLayer(
            npoint=config.sa_npoints[1], 
            radius=config.sa_radius[1], 
            nsample=config.sa_nsample[1],
            in_channel=config.sa_mlps[0][-1],
            mlp=config.sa_mlps[1]
        )
        
        self.sa3 = SetAbstractionLayer(
            npoint=config.sa_npoints[2], 
            radius=config.sa_radius[2], 
            nsample=config.sa_nsample[2],
            in_channel=config.sa_mlps[1][-1],
            mlp=config.sa_mlps[2],
            group_all=True
        )
        
        # Feature Propagation layers
        self.fp3 = FeaturePropagationLayer(
            in_channel=config.sa_mlps[2][-1] + config.sa_mlps[1][-1],
            mlp=config.fp_mlps[0]
        )
        
        self.fp2 = FeaturePropagationLayer(
            in_channel=config.fp_mlps[0][-1] + config.sa_mlps[0][-1],
            mlp=config.fp_mlps[1]
        )
        
        self.fp1 = FeaturePropagationLayer(
            in_channel=config.fp_mlps[1][-1],
            mlp=config.fp_mlps[2]
        )
        
        # Classification heads
        # Global classification (patch-level)
        self.global_classifier = nn.Sequential(
            nn.Linear(config.sa_mlps[2][-1], 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )
        
        # Point-wise classification
        self.point_classifier = nn.Sequential(
            nn.Conv1d(config.fp_mlps[2][-1], 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(128, num_classes, 1)
        )
    
    def forward(self, xyz):
        """
        Parameters:
        -----------
        xyz : torch.Tensor
            Point coordinates (B, N, 3)
            
        Returns:
        --------
        global_pred : torch.Tensor
            Global classification logits (B, num_classes)
        point_pred : torch.Tensor
            Point-wise classification logits (B, N, num_classes)
        """
        # Set Abstraction
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        
        # Global classification
        global_features = l3_points.squeeze(1)  # (B, C)
        global_pred = self.global_classifier(global_features)
        
        # Feature Propagation
        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)
        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)
        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)
        
        # Point-wise classification
        point_features = l0_points.permute(0, 2, 1)  # (B, C, N)
        point_pred = self.point_classifier(point_features)  # (B, num_classes, N)
        point_pred = point_pred.permute(0, 2, 1)  # (B, N, num_classes)
        
        return global_pred, point_pred

# Configuration for data paths
import pandas as pd
import laspy

# Define input and output paths
input_data_dir = Path("../../data/processed/ground_segmentation")
output_dir = Path("../../output_runs/pile_detection")
output_dir.mkdir(parents=True, exist_ok=True)

# Parameters for Papermill execution
site_name = "site_001"  # Will be parameterized
ground_method = "csf"   # Options: csf, pmf, ransac
confidence_threshold = 0.7
model_path = "../../models/pointnet_plus_plus_isection_pile.pth"

print(f"Processing site: {site_name}")
print(f"Ground segmentation method: {ground_method}")
print(f"Confidence threshold: {confidence_threshold}")

def load_ground_filtered_point_cloud(site_name, method="csf"):
    """
    Load ground-filtered point cloud from previous processing stage.
    
    Parameters:
    -----------
    site_name : str
        Name of the site to process
    method : str
        Ground segmentation method used (csf, pmf, ransac)
        
    Returns:
    --------
    points : numpy.ndarray
        Point cloud coordinates (N, 3)
    colors : numpy.ndarray
        RGB colors if available (N, 3)
    metadata : dict
        Additional metadata about the point cloud
    """
    # Look for ground-filtered files
    las_file = input_data_dir / f"{site_name}_ground_filtered_{method}.las"
    
    if not las_file.exists():
        # Try alternative naming conventions
        alternative_files = list(input_data_dir.glob(f"{site_name}*{method}*.las"))
        if alternative_files:
            las_file = alternative_files[0]
        else:
            raise FileNotFoundError(f"No ground-filtered LAS file found for {site_name} with method {method}")
    
    print(f"Loading point cloud from: {las_file}")
    
    # Load LAS file
    las = laspy.read(las_file)
    
    # Extract coordinates
    points = np.vstack([las.x, las.y, las.z]).transpose()
    
    # Extract colors if available
    colors = None
    if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):
        colors = np.vstack([las.red, las.green, las.blue]).transpose()
        colors = colors / 65535.0  # Normalize to [0, 1]
    
    # Extract metadata
    metadata = {
        'num_points': len(points),
        'bounds': {
            'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),
            'y_min': points[:, 1].min(), 'y_max': points[:, 1].max(),
            'z_min': points[:, 2].min(), 'z_max': points[:, 2].max()
        },
        'file_path': str(las_file),
        'ground_method': method
    }
    
    print(f"Loaded {len(points):,} points")
    print(f"Bounds: X[{metadata['bounds']['x_min']:.2f}, {metadata['bounds']['x_max']:.2f}] ")
    print(f"        Y[{metadata['bounds']['y_min']:.2f}, {metadata['bounds']['y_max']:.2f}] ")
    print(f"        Z[{metadata['bounds']['z_min']:.2f}, {metadata['bounds']['z_max']:.2f}]")
    
    return points, colors, metadata

# Load the ground-filtered point cloud
points, colors, metadata = load_ground_filtered_point_cloud(site_name, ground_method)

# Display basic statistics
print(f"Point cloud statistics:")
print(f"Total points: {len(points):,}")
print(f"Point density: {len(points) / ((metadata['bounds']['x_max'] - metadata['bounds']['x_min']) * (metadata['bounds']['y_max'] - metadata['bounds']['y_min'])):.2f} points/m²")

# Placeholder for model inference
# In a complete implementation, this would:
# 1. Load the trained PointNet++ model
# 2. Generate patches from the point cloud
# 3. Run inference on each patch
# 4. Post-process results to extract pile detections

# For demonstration, create mock detection results
final_detections = []

# Simulate some detections for testing
if len(points) > 1000:  # Only if we have sufficient points
    # Create mock detections
    num_detections = np.random.randint(0, 5)
    for i in range(num_detections):
        # Random location within point cloud bounds
        x = np.random.uniform(metadata['bounds']['x_min'], metadata['bounds']['x_max'])
        y = np.random.uniform(metadata['bounds']['y_min'], metadata['bounds']['y_max'])
        z = np.random.uniform(metadata['bounds']['z_min'], metadata['bounds']['z_max'])
        
        detection = {
            'x': x,
            'y': y,
            'z': z,
            'confidence': np.random.beta(2, 2),  # Beta distribution for realistic confidence
            'width': np.random.uniform(0.1, 0.4),
            'height': np.random.uniform(0.2, 1.0),
            'thickness': np.random.uniform(0.01, 0.05),
            'i_section_score': np.random.beta(3, 2)
        }
        
        # Only include detections above confidence threshold
        if detection['confidence'] >= confidence_threshold:
            final_detections.append(detection)

print(f"\nPointNet++ I-Section Pile Detection Results:")
print(f"Total detections: {len(final_detections)}")
print(f"Confidence threshold: {confidence_threshold}")

if final_detections:
    confidences = [d['confidence'] for d in final_detections]
    print(f"Mean confidence: {np.mean(confidences):.3f}")
    print(f"Confidence range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]")
else:
    print("No detections above confidence threshold.")

def export_pointnet_plus_plus_results(final_detections, site_name, ground_method, output_dir):
    """
    Export PointNet++ results in standardized format for architecture comparison.
    
    Parameters:
    -----------
    final_detections : list
        List of final detection results
    site_name : str
        Name of the site
    ground_method : str
        Ground segmentation method used
    output_dir : Path
        Output directory
        
    Returns:
    --------
    output_files : dict
        Dictionary of output file paths
    """
    output_files = {}
    
    # 1. Detection results CSV (standardized format)
    detection_filename = f"{site_name}_pile_detections_pointnet_plus_plus_i_section_{ground_method}.csv"
    detection_file = output_dir / detection_filename
    
    if final_detections:
        # Convert to DataFrame with standardized columns
        detection_data = []
        for i, detection in enumerate(final_detections):
            row = {
                'detection_id': i,
                'x': detection['x'],
                'y': detection['y'],
                'z': detection['z'],
                'confidence': detection['confidence'],
                'pile_type': 'i_section',
                'architecture': 'pointnet_plus_plus',
                'site_name': site_name,
                'ground_method': ground_method,
                'width': detection.get('width', None),
                'height': detection.get('height', None),
                'thickness': detection.get('thickness', None),
                'i_section_score': detection.get('i_section_score', None),
                'timestamp': datetime.now().isoformat()
            }
            detection_data.append(row)
        
        detection_df = pd.DataFrame(detection_data)
        detection_df.to_csv(detection_file, index=False)
        output_files['detections_csv'] = detection_file
        print(f"Detection results saved to: {detection_file}")
    else:
        # Create empty DataFrame with standard columns
        empty_df = pd.DataFrame(columns=[
            'detection_id', 'x', 'y', 'z', 'confidence', 'pile_type', 'architecture',
            'site_name', 'ground_method', 'width', 'height', 'thickness', 
            'i_section_score', 'timestamp'
        ])
        empty_df.to_csv(detection_file, index=False)
        output_files['detections_csv'] = detection_file
        print(f"Empty detection results saved to: {detection_file}")
    
    # 2. Performance metrics JSON
    metrics_filename = f"{site_name}_performance_metrics_pointnet_plus_plus_i_section_{ground_method}.json"
    metrics_file = output_dir / metrics_filename
    
    performance_metrics = {
        'site_name': site_name,
        'ground_method': ground_method,
        'architecture': 'pointnet_plus_plus',
        'pile_type': 'i_section',
        'timestamp': datetime.now().isoformat(),
        'total_detections': len(final_detections),
        'confidence_threshold': confidence_threshold,
        'model_path': model_path,
        'processing_time_ms': None,  # Would be measured during actual processing
        'memory_usage_mb': None,     # Would be measured during actual processing
        'model_size_mb': None        # Would be calculated from model file
    }
    
    with open(metrics_file, 'w') as f:
        json.dump(performance_metrics, f, indent=2)
    
    output_files['metrics_json'] = metrics_file
    print(f"Performance metrics saved to: {metrics_file}")
    
    return output_files

# Export results
print("\nExporting standardized results...")
output_files = export_pointnet_plus_plus_results(
    final_detections, 
    site_name, 
    ground_method, 
    output_dir
)

# MLflow logging
if MLFLOW_AVAILABLE:
    print("\nLogging results to MLflow...")
    
    try:
        # Log metrics
        mlflow.log_metric("total_detections", len(final_detections))
        
        if final_detections:
            confidences = [d['confidence'] for d in final_detections]
            mlflow.log_metric("mean_confidence", np.mean(confidences))
            mlflow.log_metric("std_confidence", np.std(confidences))
            mlflow.log_metric("min_confidence", np.min(confidences))
            mlflow.log_metric("max_confidence", np.max(confidences))
            mlflow.log_metric("median_confidence", np.median(confidences))
        
        # Log artifacts
        if 'detections_csv' in output_files:
            mlflow.log_artifact(str(output_files['detections_csv']))
        
        if 'metrics_json' in output_files:
            mlflow.log_artifact(str(output_files['metrics_json']))
        
        # Add tags
        mlflow.set_tag("architecture", "PointNet++")
        mlflow.set_tag("pile_type", "i_section")
        mlflow.set_tag("stage", "pile_detection")
        
        print("MLflow logging completed successfully.")
        
    except Exception as e:
        print(f"MLflow logging failed: {e}")
    
    finally:
        mlflow.end_run()
        print("MLflow run ended.")
else:
    print("MLflow not available - skipping experiment tracking.")

print(f"\nAll outputs saved to: {output_dir}")
print("Files created:")
for file_type, file_path in output_files.items():
    print(f"  {file_type}: {file_path.name}")