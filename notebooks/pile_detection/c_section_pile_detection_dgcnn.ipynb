# Papermill parameters - can be overridden during execution
site_name = "site_001"
ground_method = "csf"  # Options: csf, pmf, ransac
confidence_threshold = 0.7
model_path = "../../models/dgcnn_csection_pile.pth"
input_data_dir = "../../data/processed/ground_segmentation"
output_dir = "../../output_runs/pile_detection"
mlflow_experiment_name = "pile_detection_dgcnn"
mlflow_run_name = f"c_section_{site_name}_{ground_method}"
enable_cross_validation = True
cv_folds = 5
enable_enhanced_analysis = True

# Install required packages
#!pip install torch torchvision torch-geometric torch-points3d open3d matplotlib numpy scipy pandas
#!pip install scikit-learn plotly
!python -m pip install open3d laspy

# Import libraries
import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import open3d as o3d
from sklearn.neighbors import NearestNeighbors
from sklearn.cluster import DBSCAN
from scipy.spatial import ConvexHull
from scipy.spatial.distance import cdist

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F

#from torch.utils.data import Dataset, DataLoader
from torch_geometric.loader import DataLoader  

from torch_geometric.nn import DynamicEdgeConv, global_max_pool
from torch_geometric.data import Data, Batch

import warnings
warnings.filterwarnings('ignore')

print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

class CSectionPileConfig:
    """
    Configuration for C-section pile detection.
    """
    def __init__(self):
        # C-section dimensions (typical ranges)
        self.flange_width_range = (0.05, 0.3)  # meters
        self.web_height_range = (0.1, 0.8)  # meters
        self.thickness_range = (0.005, 0.03)  # meters
        self.opening_width_range = (0.08, 0.25)  # meters (gap between flanges)
        self.height_range = (0.1, 1.0)  # meters
        
        # Detection parameters
        self.patch_size = 2.0  # meters
        self.min_points_per_patch = 100
        self.overlap_ratio = 0.5
        
        # DGCNN parameters
        self.k_neighbors = 20
        self.num_points = 1024
        self.feature_dims = [64, 128, 256, 512]
        self.num_classes = 2  # pile vs non-pile

config = CSectionPileConfig()
print("C-Section Pile Detection Configuration:")
print(f"Flange width range: {config.flange_width_range} m")
print(f"Web height range: {config.web_height_range} m")
print(f"Opening width range: {config.opening_width_range} m")
print(f"Thickness range: {config.thickness_range} m")
print(f"Patch size: {config.patch_size} m")

def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):
    """
    Generate overlapping patches from a point cloud.
    """
    # Calculate bounds
    min_coords = points.min(axis=0)
    max_coords = points.max(axis=0)
    
    # Calculate step size
    step_size = patch_size * (1 - overlap_ratio)
    
    patches = []
    patch_id = 0
    
    # Generate grid of patch centers
    x_centers = np.arange(min_coords[0], max_coords[0], step_size)
    y_centers = np.arange(min_coords[1], max_coords[1], step_size)
    
    for x_center in x_centers:
        for y_center in y_centers:
            # Define patch bounds
            x_min = x_center - patch_size / 2
            x_max = x_center + patch_size / 2
            y_min = y_center - patch_size / 2
            y_max = y_center + patch_size / 2
            
            # Find points within patch
            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &
                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))
            
            patch_points = points[mask]
            
            if len(patch_points) >= min_points:
                # Center the patch points
                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])
                centered_points = patch_points - patch_center
                
                patch_data = {
                    'id': patch_id,
                    'points': centered_points,
                    'original_points': patch_points,
                    'center': patch_center,
                    'bounds': (x_min, y_min, x_max, y_max),
                    'num_points': len(patch_points)
                }
                
                if colors is not None:
                    patch_data['colors'] = colors[mask]
                
                patches.append(patch_data)
                patch_id += 1
    
    print(f"Generated {len(patches)} patches from point cloud")
    return patches

def analyze_c_section_geometry(points, tolerance=0.05):
    """
    Analyze point cloud patch for C-section pile geometry.    
    """
    if len(points) < 10:
        return None
    
    # Project points to XY plane for cross-section analysis
    xy_points = points[:, :2]
    
    # Find convex hull
    try:
        hull = ConvexHull(xy_points)
        hull_points = xy_points[hull.vertices]
    except:
        return None
    
    # Calculate bounding box
    min_coords = points.min(axis=0)
    max_coords = points.max(axis=0)
    dimensions = max_coords - min_coords
    
    # Analyze cross-sectional shape for C-section
    # For C-section, we expect:
    # 1. U-shaped or C-shaped cross-section
    # 2. Two flanges connected by a web
    # 3. Opening on one side
    # 4. Specific width-to-height ratios
    
    # Calculate aspect ratios
    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0
    if xy_aspect < 1:
        xy_aspect = 1 / xy_aspect
    
    # Analyze point distribution for C-section pattern
    # Cluster points in cross-section
    clustering = DBSCAN(eps=tolerance, min_samples=5)
    cluster_labels = clustering.fit_predict(xy_points)
    
    unique_labels = np.unique(cluster_labels)
    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)
    
    # Calculate density distribution for C-shape detection
    center_x, center_y = xy_points.mean(axis=0)
    
    # Divide into radial sectors to detect C-shape opening
    angles = np.arctan2(xy_points[:, 1] - center_y, xy_points[:, 0] - center_x)
    angle_bins = np.linspace(-np.pi, np.pi, 16)
    angle_hist, _ = np.histogram(angles, bins=angle_bins)
    
    # Find potential opening (sector with low point density)
    min_density_sector = np.argmin(angle_hist)
    opening_ratio = angle_hist[min_density_sector] / (angle_hist.max() + 1e-6)
    
    # Calculate concavity measure
    # C-sections should have higher concavity than solid shapes
    hull_area = hull.volume if hasattr(hull, 'volume') else 0
    point_area = len(points) * tolerance * tolerance  # Approximate area
    concavity = 1 - (point_area / (hull_area + 1e-6))
    
    # Analyze symmetry (C-sections often have some symmetry)
    # Check for reflection symmetry across different axes
    reflected_x = np.column_stack([-xy_points[:, 0], xy_points[:, 1]])
    reflected_y = np.column_stack([xy_points[:, 0], -xy_points[:, 1]])
    
    # Calculate symmetry scores (simplified)
    x_symmetry = np.mean(np.min(cdist(xy_points, reflected_x), axis=1))
    y_symmetry = np.mean(np.min(cdist(xy_points, reflected_y), axis=1))
    
    # Calculate features
    features = {
        'num_points': len(points),
        'dimensions': dimensions,
        'xy_aspect_ratio': xy_aspect,
        'height': dimensions[2],
        'width': max(dimensions[0], dimensions[1]),
        'thickness': min(dimensions[0], dimensions[1]),
        'num_clusters': num_clusters,
        'hull_area': hull_area,
        'opening_ratio': opening_ratio,
        'concavity': concavity,
        'x_symmetry': x_symmetry,
        'y_symmetry': y_symmetry,
        'compactness': len(points) / hull_area if hull_area > 0 else 0,
        'c_shape_score': opening_ratio * concavity * (1 - min(x_symmetry, y_symmetry))
    }
    
    return features

class DGCNNCSectionPile(nn.Module):
    """
    DGCNN model for C-section pile detection.
    """
    def __init__(self, k=20, feature_dims=[64, 128, 256, 512], num_classes=2, dropout=0.5):
        super(DGCNNCSectionPile, self).__init__()
        self.k = k
        self.num_classes = num_classes
        
        # Edge convolution layers
        self.conv1 = DynamicEdgeConv(nn.Sequential(
            nn.Linear(6, feature_dims[0]),  # 6 = 3 (xyz) * 2 (point and neighbor)
            nn.BatchNorm1d(feature_dims[0]),
            nn.ReLU(),
            nn.Linear(feature_dims[0], feature_dims[0])
        ), k=k, aggr='max')
        
        self.conv2 = DynamicEdgeConv(nn.Sequential(
            nn.Linear(feature_dims[0] * 2, feature_dims[1]),
            nn.BatchNorm1d(feature_dims[1]),
            nn.ReLU(),
            nn.Linear(feature_dims[1], feature_dims[1])
        ), k=k, aggr='max')
        
        self.conv3 = DynamicEdgeConv(nn.Sequential(
            nn.Linear(feature_dims[1] * 2, feature_dims[2]),
            nn.BatchNorm1d(feature_dims[2]),
            nn.ReLU(),
            nn.Linear(feature_dims[2], feature_dims[2])
        ), k=k, aggr='max')
        
        self.conv4 = DynamicEdgeConv(nn.Sequential(
            nn.Linear(feature_dims[2] * 2, feature_dims[3]),
            nn.BatchNorm1d(feature_dims[3]),
            nn.ReLU(),
            nn.Linear(feature_dims[3], feature_dims[3])
        ), k=k, aggr='max')
        
        # Global feature aggregation
        total_features = sum(feature_dims)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(total_features, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )
        
        # Point-wise classification head
        self.point_classifier = nn.Sequential(
            nn.Linear(total_features, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(128, num_classes)
        )
    
    def forward(self, data):
        x, batch = data.x, data.batch
        
        # Extract features through edge convolutions
        x1 = self.conv1(x, batch)
        x2 = self.conv2(x1, batch)
        x3 = self.conv3(x2, batch)
        x4 = self.conv4(x3, batch)
        
        # Concatenate all features
        x_concat = torch.cat([x1, x2, x3, x4], dim=1)
        
        # Global classification (patch-level)
        global_features = global_max_pool(x_concat, batch)
        global_pred = self.classifier(global_features)
        
        # Point-wise classification
        point_pred = self.point_classifier(x_concat)
        
        return global_pred, point_pred

# Configuration for data paths
import pandas as pd
import laspy

# Define input and output paths
input_data_dir = Path("../../data/processed/ground_segmentation")
output_dir = Path("../../output_runs/pile_detection")
output_dir.mkdir(parents=True, exist_ok=True)

# Parameters for Papermill execution
site_name = "castro"  # Will be parameterized
ground_method = "ransac"   # Options: csf, pmf, ransac
confidence_threshold = 0.3
model_path = "../../models/dgcnn_csection_pile.pth"

print(f"Processing site: {site_name}")
print(f"Ground segmentation method: {ground_method}")
print(f"Confidence threshold: {confidence_threshold}")

def load_ground_filtered_point_cloud(site_name=None, method="csf",input_path=None):
    """
    Load ground-filtered point cloud from previous processing stage.
    """
    # Look for ground-filtered files
    if input_path:
        las_file = Path(input_path)
        if not las_file.exists():
            raise FileNotFoundError(f"File not found: {las_file}")
    else:
        if not site_name:
            raise ValueError("site_name must be provided if input_path is not specified.")

        las_file = input_data_dir / f"{site_name}_ground_filtered_{method}.las"
    
        if not las_file.exists():
            # Try alternative naming conventions
            alternative_files = list(input_data_dir.glob(f"{site_name}*{method}*.las"))
            if alternative_files:
                las_file = alternative_files[0]
            else:
                raise FileNotFoundError(f"No ground-filtered LAS file found for {site_name} with method {method}")
    
    print(f"Loading point cloud from: {las_file}")
    
    ext = las_file.suffix.lower()

    if ext == ".las" or ext == ".laz":
        # Load LAS file
        las = laspy.read(str(las_file))

        # Extract coordinates
        points = np.vstack([las.x, las.y, las.z]).transpose()
        
        # Extract colors if available
        colors = None
        if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):
            colors = np.vstack([las.red, las.green, las.blue]).transpose()
            colors = colors / 65535.0  # Normalize to [0, 1]

    elif ext == ".ply":
        import open3d as o3d
        pcd = o3d.io.read_point_cloud(str(las_file))
        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors) if pcd.has_colors() else None

    else:
        raise ValueError(f"Unsupported file extension: {ext}")

    
    # Extract metadata
    metadata = {
        'num_points': len(points),
        'bounds': {
            'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),
            'y_min': points[:, 1].min(), 'y_max': points[:, 1].max(),
            'z_min': points[:, 2].min(), 'z_max': points[:, 2].max()
        },
        'file_path': str(las_file),
        'ground_method': method
    }
    
    print(f"Loaded {len(points):,} points")
    print(f"Bounds: X[{metadata['bounds']['x_min']:.2f}, {metadata['bounds']['x_max']:.2f}] ")
    print(f"        Y[{metadata['bounds']['y_min']:.2f}, {metadata['bounds']['y_max']:.2f}] ")
    print(f"        Z[{metadata['bounds']['z_min']:.2f}, {metadata['bounds']['z_max']:.2f}]")
    
    return points, colors, metadata

# Load the ground-filtered point cloud using an explicit .ply file path
input_ply_path = "../preprocessing/ground_segmentation/output_runs/Castro_ransac_pmf_20250623_185602/ransac_only_nonground.ply"
points, colors, metadata = load_ground_filtered_point_cloud(input_path=input_ply_path)


# Display basic statistics
print(f"Point Cloud Statistics for Site: {site_name} (Method: {ground_method})")
print(f"Total points: {len(points):,}")

# Calculate area from bounds
x_range = metadata['bounds']['x_max'] - metadata['bounds']['x_min']
y_range = metadata['bounds']['y_max'] - metadata['bounds']['y_min']
area_m2 = x_range * y_range

# Safeguard against division by zero
if area_m2 > 0:
    point_density = len(points) / area_m2
    print(f"Point density: {point_density:.2f} points/m²")
else:
    print("Invalid bounding box dimensions — cannot compute point density.")


from torch.utils.data import Dataset

class PointCloudDataset(Dataset):
    """
    Dataset class for point cloud patches.
    """
    def __init__(self, patches, num_points=1024, augment=False):
        self.patches = patches
        self.num_points = num_points
        self.augment = augment
    
    def __len__(self):
        return len(self.patches)
    
    def __getitem__(self, idx):
        patch = self.patches[idx]
        points = patch['points']
        
        # Sample or pad points to fixed size
        if len(points) >= self.num_points:
            # Random sampling
            indices = np.random.choice(len(points), self.num_points, replace=False)
            points = points[indices]
        else:
            # Pad with repeated points
            repeat_indices = np.random.choice(len(points), self.num_points - len(points), replace=True)
            points = np.vstack([points, points[repeat_indices]])
        
        # Data augmentation (if enabled)
        if self.augment:
            # Random rotation around Z-axis
            angle = np.random.uniform(0, 2 * np.pi)
            cos_a, sin_a = np.cos(angle), np.sin(angle)
            rotation_matrix = np.array([
                [cos_a, -sin_a, 0],
                [sin_a, cos_a, 0],
                [0, 0, 1]
            ])
            points = points @ rotation_matrix.T
            
            # Random jittering
            noise = np.random.normal(0, 0.01, points.shape)
            points += noise
        
        # Convert to torch tensor
        points_tensor = torch.FloatTensor(points)
        
        # Create PyTorch Geometric data object
        data = Data(x=points_tensor)
        
        return data, patch['id']

def preprocess_patches_for_dgcnn(patches, num_points=1024):
    """
    Preprocess patches for DGCNN input.
    """
    print(f"Preprocessing {len(patches)} patches for DGCNN input")
    print(f"Target points per patch: {num_points}")
    
    # Filter patches with sufficient points
    valid_patches = [p for p in patches if len(p['points']) >= config.min_points_per_patch]
    print(f"Valid patches after filtering: {len(valid_patches)}")
    
    # Create dataset
    dataset = PointCloudDataset(valid_patches, num_points=num_points, augment=False)
    
    return dataset

# Generate patches from the loaded point cloud
print("Generating patches from point cloud...")
patches = generate_patches_from_point_cloud(
    points, 
    colors=colors,
    patch_size=config.patch_size,
    overlap_ratio=config.overlap_ratio,
    min_points=config.min_points_per_patch
)

print(f"Generated {len(patches)} patches")
if len(patches) > 0:
    print(f"Average points per patch: {np.mean([p['num_points'] for p in patches]):.1f}")
    print(f"Min points per patch: {min([p['num_points'] for p in patches])}")
    print(f"Max points per patch: {max([p['num_points'] for p in patches])}")

# Preprocess patches for DGCNN
dataset = preprocess_patches_for_dgcnn(patches, num_points=config.num_points)
dataloader = DataLoader(dataset, batch_size=8, shuffle=False, num_workers=0)

print(f"Created dataset with {len(dataset)} patches")
print(f"Batch size: 8")
print(f"Number of batches: {len(dataloader)}")

def load_dgcnn_model(model_path, device):
    """
    Load trained DGCNN model for inference.
    """
    # Initialize model
    model = DGCNNCSectionPile(
        k=config.k_neighbors,
        feature_dims=config.feature_dims,
        num_classes=config.num_classes
    )
    
    # Load model weights if available
    if Path(model_path).exists():
        print(f"Loading model weights from: {model_path}")
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Model loaded successfully (epoch {checkpoint.get('epoch', 'unknown')})")
    else:
        print(f"Warning: Model file not found at {model_path}")
        print("Using randomly initialized model (for demonstration purposes)")
    
    model.to(device)
    model.eval()
    
    return model

def run_inference(model, dataloader, device, confidence_threshold=0.7):
    """
    Run inference on all patches using the DGCNN model.
    """
    results = []
    
    print(f"Running inference on {len(dataloader)} batches...")
    
    with torch.no_grad():
        for batch_idx, (batch_data, patch_ids) in enumerate(dataloader):
            # Move data to device
            batch_data = batch_data.to(device)
            
            # Forward pass
            global_pred, point_pred = model(batch_data)
            
            # Apply softmax to get probabilities
            global_probs = F.softmax(global_pred, dim=1)
            point_probs = F.softmax(point_pred, dim=1)
            
            # Process each sample in the batch
            batch_size = len(patch_ids)
            for i in range(batch_size):
                patch_id = patch_ids[i]
                
                # Global prediction (patch-level)
                global_confidence = global_probs[i, 1].item()  # Probability of pile class
                is_pile_patch = global_confidence > confidence_threshold
                
                # Point-wise predictions
                start_idx = i * config.num_points
                end_idx = start_idx + config.num_points
                point_confidences = point_probs[start_idx:end_idx, 1].cpu().numpy()
                
                result = {
                    'patch_id': patch_id,
                    'global_confidence': global_confidence,
                    'is_pile_patch': is_pile_patch,
                    'point_confidences': point_confidences,
                    'num_pile_points': (point_confidences > confidence_threshold).sum()
                }
                
                results.append(result)
            
            if (batch_idx + 1) % 10 == 0:
                print(f"Processed {batch_idx + 1}/{len(dataloader)} batches")
    
    print(f"Inference completed. Processed {len(results)} patches.")
    return results

# Load the DGCNN model
model = load_dgcnn_model(model_path, device)

# Display model information
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
print(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")

# Run inference on all patches
inference_results = run_inference(model, dataloader, device, confidence_threshold)

# Display inference statistics
pile_patches = [r for r in inference_results if r['is_pile_patch']]
total_pile_points = sum(r['num_pile_points'] for r in inference_results)

print(f"Inference Results:")
print(f"Total patches processed: {len(inference_results)}")
print(f"Patches with pile detections: {len(pile_patches)}")
print(f"Detection rate: {len(pile_patches)/len(inference_results)*100:.1f}%")
print(f"Total pile points detected: {total_pile_points:,}")

def extract_pile_centers(patches, inference_results, confidence_threshold=0.7):
    """
    Extract pile centers from detection results.
    
    Parameters:
    -----------
    patches : list
        Original patch data
    inference_results : list
        Inference results from DGCNN
    confidence_threshold : float
        Minimum confidence for pile detection
        
    Returns:
    --------
    pile_detections : list
        List of detected pile centers with metadata
    """
    pile_detections = []
    
    # Create patch lookup dictionary
    patch_dict = {p['id']: p for p in patches}
    
    for result in inference_results:
        if not result['is_pile_patch']:
            continue
            
       # patch_id = result['patch_id']
        patch_id = int(result['patch_id'])
        patch = patch_dict[patch_id]
        
        # Get points with high pile confidence
        point_confidences = result['point_confidences']
        pile_mask = point_confidences > confidence_threshold
        
        if pile_mask.sum() < 10:  # Minimum points for valid detection
            continue
        
        # Get pile points in original coordinates
        patch_points = patch['original_points']
        
        # Sample points to match inference results
        if len(patch_points) >= config.num_points:
            indices = np.random.choice(len(patch_points), config.num_points, replace=False)
            sampled_points = patch_points[indices]
        else:
            repeat_indices = np.random.choice(len(patch_points), config.num_points - len(patch_points), replace=True)
            sampled_points = np.vstack([patch_points, patch_points[repeat_indices]])
        
        pile_points = sampled_points[pile_mask]
        
        # Calculate pile center
        pile_center = pile_points.mean(axis=0)
        
        # Analyze pile geometry
        geometry_features = analyze_c_section_geometry(pile_points)
        
        detection = {
            'x': pile_center[0],
            'y': pile_center[1],
            'z': pile_center[2],
            'pile_type': 'C-section',
            'confidence': result['global_confidence'],
            'num_points': len(pile_points),
            'patch_id': patch_id,
            'width': geometry_features['width'] if geometry_features else 0,
            'height': geometry_features['height'] if geometry_features else 0,
            'thickness': geometry_features['thickness'] if geometry_features else 0,
            'c_shape_score': geometry_features['c_shape_score'] if geometry_features else 0,
            'opening_ratio': geometry_features['opening_ratio'] if geometry_features else 0
        }
        
        pile_detections.append(detection)
    
    print(f"Extracted {len(pile_detections)} pile centers")
    return pile_detections

def cluster_nearby_detections(detections, distance_threshold=1.0):
    """
    Cluster nearby pile detections to remove duplicates.
    
    Parameters:
    -----------
    detections : list
        List of pile detections
    distance_threshold : float
        Maximum distance for clustering
        
    Returns:
    --------
    clustered_detections : list
        Clustered and filtered detections
    """
    if len(detections) == 0:
        return []
    
    # Extract coordinates
    coords = np.array([[d['x'], d['y'], d['z']] for d in detections])
    
    # Perform clustering
    clustering = DBSCAN(eps=distance_threshold, min_samples=1)
    cluster_labels = clustering.fit_predict(coords)
    
    clustered_detections = []
    
    # Process each cluster
    for cluster_id in np.unique(cluster_labels):
        if cluster_id == -1:  # Noise points
            continue
            
        cluster_mask = cluster_labels == cluster_id
        cluster_detections = [detections[i] for i in np.where(cluster_mask)[0]]
        
        # Select detection with highest confidence
        best_detection = max(cluster_detections, key=lambda x: x['confidence'])
        
        # Update with cluster statistics
        cluster_coords = coords[cluster_mask]
        best_detection['cluster_size'] = len(cluster_detections)
        best_detection['cluster_std'] = np.std(cluster_coords, axis=0).mean()
        
        clustered_detections.append(best_detection)
    
    print(f"Clustered {len(detections)} detections into {len(clustered_detections)} final detections")
    return clustered_detections

# Extract pile centers from inference results
pile_detections = extract_pile_centers(patches, inference_results, confidence_threshold)

# Cluster nearby detections to remove duplicates
final_detections = cluster_nearby_detections(pile_detections, distance_threshold=1.5)

print(f"Final pile detection results:")
print(f"Total detections: {len(final_detections)}")
if len(final_detections) > 0:
    avg_confidence = np.mean([d['confidence'] for d in final_detections])
    print(f"Average confidence: {avg_confidence:.3f}")
    print(f"Confidence range: [{min(d['confidence'] for d in final_detections):.3f}, {max(d['confidence'] for d in final_detections):.3f}]")
    
    # C-section specific metrics
    avg_c_score = np.mean([d['c_shape_score'] for d in final_detections])
    print(f"Average C-shape score: {avg_c_score:.3f}")

def save_detection_results(detections, output_dir, site_name, method):
    """
    Save pile detection results to CSV file.
    
    Parameters:
    -----------
    detections : list
        List of pile detections
    output_dir : Path
        Output directory
    site_name : str
        Site name
    method : str
        Detection method name
        
    Returns:
    --------
    output_file : Path
        Path to saved CSV file
    """
    # Create DataFrame
    df = pd.DataFrame(detections)
    
    # Add metadata columns
    df['site_name'] = site_name
    df['detection_method'] = 'DGCNN'
    df['ground_method'] = method
    df['timestamp'] = pd.Timestamp.now()
    
    # Reorder columns
    column_order = [
        'site_name', 'pile_type', 'x', 'y', 'z', 'confidence',
        'width', 'height', 'thickness', 'c_shape_score', 'opening_ratio',
        'num_points', 'cluster_size', 'cluster_std', 'patch_id',
        'detection_method', 'ground_method', 'timestamp'
    ]
    
    # Only include columns that exist
    available_columns = [col for col in column_order if col in df.columns]
    df = df[available_columns]
    
    # Save to CSV
    output_file = output_dir / f"{site_name}_pile_detections_dgcnn_csection_{method}.csv"
    df.to_csv(output_file, index=False)
    
    print(f"Saved {len(df)} detections to: {output_file}")
    return output_file

def create_detection_visualization(points, detections, output_dir, site_name):
    """
    Create visualization of pile detections.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Original point cloud
    detections : list
        Pile detections
    output_dir : Path
        Output directory
    site_name : str
        Site name
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot 1: Top-down view
    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], 
               cmap='terrain', alpha=0.5, s=0.1)
    
    if len(detections) > 0:
        det_x = [d['x'] for d in detections]
        det_y = [d['y'] for d in detections]
        det_conf = [d['confidence'] for d in detections]
        
        scatter = ax1.scatter(det_x, det_y, c=det_conf, cmap='Reds', 
                            s=100, edgecolors='black', linewidth=1)
        plt.colorbar(scatter, ax=ax1, label='Confidence')
        
        # Add detection labels
        for i, det in enumerate(detections):
            ax1.annotate(f'{i+1}', (det['x'], det['y']), 
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, color='white', weight='bold')
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title(f'C-Section Pile Detections - Top View\n{site_name}')
    ax1.grid(True, alpha=0.3)
    ax1.set_aspect('equal')
    
    # Plot 2: Confidence distribution
    if len(detections) > 0:
        confidences = [d['confidence'] for d in detections]
        ax2.hist(confidences, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(confidence_threshold, color='red', linestyle='--', 
                   label=f'Threshold: {confidence_threshold}')
        ax2.set_xlabel('Confidence')
        ax2.set_ylabel('Number of Detections')
        ax2.set_title('Detection Confidence Distribution')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    else:
        ax2.text(0.5, 0.5, 'No detections found', 
                ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('No Detections')
    
    # Plot 3: C-shape score distribution
    if len(detections) > 0 and 'c_shape_score' in detections[0]:
        c_scores = [d['c_shape_score'] for d in detections]
        ax3.hist(c_scores, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
        ax3.set_xlabel('C-Shape Score')
        ax3.set_ylabel('Number of Detections')
        ax3.set_title('C-Shape Score Distribution')
        ax3.grid(True, alpha=0.3)
    else:
        ax3.text(0.5, 0.5, 'No C-shape scores available', 
                ha='center', va='center', transform=ax3.transAxes)
        ax3.set_title('C-Shape Scores')
    
    # Plot 4: Opening ratio vs confidence
    if len(detections) > 0 and 'opening_ratio' in detections[0]:
        opening_ratios = [d['opening_ratio'] for d in detections]
        confidences = [d['confidence'] for d in detections]
        ax4.scatter(opening_ratios, confidences, alpha=0.7, s=50)
        ax4.set_xlabel('Opening Ratio')
        ax4.set_ylabel('Confidence')
        ax4.set_title('Opening Ratio vs Confidence')
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(0.5, 0.5, 'No opening ratio data', 
                ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('Opening Ratio Analysis')
    
    plt.tight_layout()
    
    # Save visualization
    viz_file = output_dir / f"{site_name}_pile_detections_dgcnn_csection_visualization.png"
    plt.savefig(viz_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Saved visualization to: {viz_file}")

# Save detection results
output_file = save_detection_results(final_detections, output_dir, site_name, ground_method)

# Create visualization
create_detection_visualization(points, final_detections, output_dir, site_name)

# Display summary statistics
print(f"\nC-Section Pile Detection Summary:")
print(f"Site: {site_name}")
print(f"Ground segmentation method: {ground_method}")
print(f"Input points: {len(points):,}")
print(f"Generated patches: {len(patches)}")
print(f"Processed patches: {len(inference_results)}")
print(f"Final detections: {len(final_detections)}")
print(f"Results saved to: {output_file}")

# Analysis of detection results
if len(final_detections) > 0:
    print("Detection Analysis:")
    
    # Confidence statistics
    confidences = [d['confidence'] for d in final_detections]
    print(f"Confidence statistics:")
    print(f"  Mean: {np.mean(confidences):.3f}")
    print(f"  Std: {np.std(confidences):.3f}")
    print(f"  Min: {np.min(confidences):.3f}")
    print(f"  Max: {np.max(confidences):.3f}")
    
    # C-section specific analysis
    if 'c_shape_score' in final_detections[0]:
        c_scores = [d['c_shape_score'] for d in final_detections]
        opening_ratios = [d['opening_ratio'] for d in final_detections]
        
        print(f"\nC-Section characteristics:")
        print(f"  Average C-shape score: {np.mean(c_scores):.3f}")
        print(f"  Average opening ratio: {np.mean(opening_ratios):.3f}")
        print(f"  C-shape score range: [{np.min(c_scores):.3f}, {np.max(c_scores):.3f}]")
    
    # Spatial distribution
    x_coords = [d['x'] for d in final_detections]
    y_coords = [d['y'] for d in final_detections]
    z_coords = [d['z'] for d in final_detections]
    
    print(f"\nSpatial distribution:")
    print(f"  X range: [{np.min(x_coords):.2f}, {np.max(x_coords):.2f}] m")
    print(f"  Y range: [{np.min(y_coords):.2f}, {np.max(y_coords):.2f}] m")
    print(f"  Z range: [{np.min(z_coords):.2f}, {np.max(z_coords):.2f}] m")
    
    # Geometric characteristics
    if 'width' in final_detections[0]:
        widths = [d['width'] for d in final_detections if d['width'] > 0]
        heights = [d['height'] for d in final_detections if d['height'] > 0]
        
        if widths:
            print(f"\nGeometric characteristics:")
            print(f"  Average width: {np.mean(widths):.3f} m")
            print(f"  Average height: {np.mean(heights):.3f} m")
    
    print(f"\nInference:")
    print(f"The DGCNN model detected {len(final_detections)} C-section pile candidates")
    print(f"with an average confidence of {np.mean(confidences):.1%}.")
    
    if 'c_shape_score' in final_detections[0]:
        avg_c_score = np.mean([d['c_shape_score'] for d in final_detections])
        if avg_c_score > 0.3:
            print("High C-shape scores indicate strong geometric evidence for C-section profiles.")
        elif avg_c_score > 0.1:
            print("Moderate C-shape scores suggest possible C-section characteristics.")
        else:
            print("Low C-shape scores may indicate weak geometric evidence or challenging conditions.")
    
    if np.mean(confidences) > 0.8:
        print("High confidence detections suggest reliable pile identification.")
    elif np.mean(confidences) > 0.6:
        print("Moderate confidence detections may require manual verification.")
    else:
        print("Low confidence detections suggest challenging conditions or model limitations.")
        
else:
    print("No C-section piles detected in the point cloud.")
    print("This could indicate:")
    print("- No C-section piles present in the surveyed area")
    print("- Insufficient point cloud quality or density")
    print("- Model confidence threshold too high")
    print("- Need for model retraining on site-specific data")
    print("- C-section profiles may be oriented differently than expected")

import ezdxf

doc = ezdxf.readfile("Drawing of Earthworks1_2.dwg")
msp = doc.modelspace()

pile_coords = []
for entity in msp.query('CIRCLE'):  # or LINE, POINT, INSERT, etc.
    x, y, z = entity.dxf.center
    pile_coords.append((x, y, z))

print("Extracted pile positions:", pile_coords[:5])
