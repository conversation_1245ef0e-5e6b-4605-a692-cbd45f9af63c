# Install required packages
!pip install torch torchvision torch-geometric torch-points3d open3d matplotlib numpy scipy pandas
!pip install scikit-learn plotly

# Import libraries
import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import open3d as o3d
from sklearn.neighbors import NearestNeighbors
from sklearn.cluster import DBSCAN
from scipy.spatial import ConvexHull
from scipy.spatial.distance import cdist

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch_geometric.nn import PointConv, global_max_pool, global_mean_pool
from torch_geometric.data import Data, Batch
from torch_geometric.nn import knn_graph

import warnings
warnings.filterwarnings('ignore')

print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

class CSectionPileConfig:
    """
    Configuration for C-section pile detection using PointNet++.
    """
    def __init__(self):
        # C-section dimensions (typical ranges)
        self.flange_width_range = (0.05, 0.3)  # meters
        self.web_height_range = (0.1, 0.8)  # meters
        self.thickness_range = (0.005, 0.03)  # meters
        self.opening_width_range = (0.08, 0.25)  # meters (gap between flanges)
        self.height_range = (0.1, 1.0)  # meters
        
        # Detection parameters
        self.patch_size = 2.0  # meters
        self.min_points_per_patch = 100
        self.overlap_ratio = 0.5
        
        # PointNet++ parameters
        self.num_points = 1024
        self.num_classes = 2  # pile vs non-pile
        
        # Set abstraction parameters
        self.sa_npoints = [512, 128, None]  # Number of points in each SA layer
        self.sa_radius = [0.2, 0.4, None]   # Radius for each SA layer
        self.sa_nsample = [64, 64, None]    # Number of samples in each SA layer
        self.sa_mlps = [[64, 64, 128], [128, 128, 256], [256, 512, 1024]]  # MLP dimensions
        
        # Feature propagation parameters
        self.fp_mlps = [[256, 256], [256, 128], [128, 128, 128]]  # FP MLP dimensions

config = CSectionPileConfig()
print("C-Section Pile Detection Configuration (PointNet++):")
print(f"Flange width range: {config.flange_width_range} m")
print(f"Web height range: {config.web_height_range} m")
print(f"Opening width range: {config.opening_width_range} m")
print(f"Thickness range: {config.thickness_range} m")
print(f"Patch size: {config.patch_size} m")
print(f"Set abstraction layers: {len(config.sa_npoints)}")
print(f"Feature propagation layers: {len(config.fp_mlps)}")

def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):
    """
    Generate overlapping patches from a point cloud.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud coordinates (N, 3)
    colors : numpy.ndarray, optional
        RGB colors (N, 3)
    patch_size : float
        Size of each patch in meters
    overlap_ratio : float
        Overlap ratio between adjacent patches
    min_points : int
        Minimum number of points per patch
        
    Returns:
    --------
    patches : list
        List of patch dictionaries containing points, colors, and metadata
    """
    # Calculate bounds
    min_coords = points.min(axis=0)
    max_coords = points.max(axis=0)
    
    # Calculate step size
    step_size = patch_size * (1 - overlap_ratio)
    
    patches = []
    patch_id = 0
    
    # Generate grid of patch centers
    x_centers = np.arange(min_coords[0], max_coords[0], step_size)
    y_centers = np.arange(min_coords[1], max_coords[1], step_size)
    
    for x_center in x_centers:
        for y_center in y_centers:
            # Define patch bounds
            x_min = x_center - patch_size / 2
            x_max = x_center + patch_size / 2
            y_min = y_center - patch_size / 2
            y_max = y_center + patch_size / 2
            
            # Find points within patch
            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &
                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))
            
            patch_points = points[mask]
            
            if len(patch_points) >= min_points:
                # Center the patch points
                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])
                centered_points = patch_points - patch_center
                
                patch_data = {
                    'id': patch_id,
                    'points': centered_points,
                    'original_points': patch_points,
                    'center': patch_center,
                    'bounds': (x_min, y_min, x_max, y_max),
                    'num_points': len(patch_points)
                }
                
                if colors is not None:
                    patch_data['colors'] = colors[mask]
                
                patches.append(patch_data)
                patch_id += 1
    
    print(f"Generated {len(patches)} patches from point cloud")
    return patches

def analyze_c_section_geometry(points, tolerance=0.05):
    """
    Analyze point cloud patch for C-section pile geometry.
    
    Parameters:
    -----------
    points : numpy.ndarray
        Point cloud patch (N, 3)
    tolerance : float
        Tolerance for geometric analysis
        
    Returns:
    --------
    features : dict
        Geometric features indicating C-section characteristics
    """
    if len(points) < 10:
        return None
    
    # Project points to XY plane for cross-section analysis
    xy_points = points[:, :2]
    
    # Find convex hull
    try:
        hull = ConvexHull(xy_points)
        hull_points = xy_points[hull.vertices]
    except:
        return None
    
    # Calculate bounding box
    min_coords = points.min(axis=0)
    max_coords = points.max(axis=0)
    dimensions = max_coords - min_coords
    
    # Analyze cross-sectional shape for C-section
    # For C-section, we expect:
    # 1. U-shaped or C-shaped cross-section
    # 2. Two flanges connected by a web
    # 3. Opening on one side
    # 4. Specific width-to-height ratios
    
    # Calculate aspect ratios
    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0
    if xy_aspect < 1:
        xy_aspect = 1 / xy_aspect
    
    # Analyze point distribution for C-section pattern
    # Cluster points in cross-section
    clustering = DBSCAN(eps=tolerance, min_samples=5)
    cluster_labels = clustering.fit_predict(xy_points)
    
    unique_labels = np.unique(cluster_labels)
    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)
    
    # Calculate density distribution for C-shape detection
    center_x, center_y = xy_points.mean(axis=0)
    
    # Divide into radial sectors to detect C-shape opening
    angles = np.arctan2(xy_points[:, 1] - center_y, xy_points[:, 0] - center_x)
    angle_bins = np.linspace(-np.pi, np.pi, 16)
    angle_hist, _ = np.histogram(angles, bins=angle_bins)
    
    # Find potential opening (sector with low point density)
    min_density_sector = np.argmin(angle_hist)
    opening_ratio = angle_hist[min_density_sector] / (angle_hist.max() + 1e-6)
    
    # Calculate concavity measure
    # C-sections should have higher concavity than solid shapes
    hull_area = hull.volume if hasattr(hull, 'volume') else 0
    point_area = len(points) * tolerance * tolerance  # Approximate area
    concavity = 1 - (point_area / (hull_area + 1e-6))
    
    # Analyze symmetry (C-sections often have some symmetry)
    # Check for reflection symmetry across different axes
    reflected_x = np.column_stack([-xy_points[:, 0], xy_points[:, 1]])
    reflected_y = np.column_stack([xy_points[:, 0], -xy_points[:, 1]])
    
    # Calculate symmetry scores (simplified)
    x_symmetry = np.mean(np.min(cdist(xy_points, reflected_x), axis=1))
    y_symmetry = np.mean(np.min(cdist(xy_points, reflected_y), axis=1))
    
    # Calculate features
    features = {
        'num_points': len(points),
        'dimensions': dimensions,
        'xy_aspect_ratio': xy_aspect,
        'height': dimensions[2],
        'width': max(dimensions[0], dimensions[1]),
        'thickness': min(dimensions[0], dimensions[1]),
        'num_clusters': num_clusters,
        'hull_area': hull_area,
        'opening_ratio': opening_ratio,
        'concavity': concavity,
        'x_symmetry': x_symmetry,
        'y_symmetry': y_symmetry,
        'compactness': len(points) / hull_area if hull_area > 0 else 0,
        'c_shape_score': opening_ratio * concavity * (1 - min(x_symmetry, y_symmetry))
    }
    
    return features

# Import the PointNet++ components from the I-section notebook
# In practice, these would be in a shared module

# For brevity, we'll reference the same SetAbstractionLayer and FeaturePropagationLayer
# classes from the I-section notebook. In a real implementation, these would be
# in a shared utils module.

# Here we'll define the C-section specific model
class PointNetPlusPlusCSectionPile(nn.Module):
    """
    PointNet++ model for C-section pile detection.
    
    Note: This uses the same architecture as I-section but will be trained
    specifically for C-section pile characteristics.
    """
    def __init__(self, num_classes=2, dropout=0.5):
        super(PointNetPlusPlusCSectionPile, self).__init__()
        self.num_classes = num_classes
        
        # For demonstration, we'll use a simplified architecture
        # In practice, you would import the full SetAbstraction and FeaturePropagation layers
        
        # Simplified feature extraction layers
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(3, 64, 1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Conv1d(512, 1024, 1),
            nn.BatchNorm1d(1024),
            nn.ReLU()
        )
        
        # Global classification (patch-level)
        self.global_classifier = nn.Sequential(
            nn.Linear(1024, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(256, num_classes)
        )
        
        # Point-wise classification
        self.point_classifier = nn.Sequential(
            nn.Conv1d(1024, 512, 1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(512, 256, 1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(256, num_classes, 1)
        )
    
    def forward(self, xyz):
        """
        Parameters:
        -----------
        xyz : torch.Tensor
            Point coordinates (B, N, 3)
            
        Returns:
        --------
        global_pred : torch.Tensor
            Global classification logits (B, num_classes)
        point_pred : torch.Tensor
            Point-wise classification logits (B, N, num_classes)
        """
        # Transpose for conv1d: (B, N, 3) -> (B, 3, N)
        x = xyz.transpose(2, 1)
        
        # Extract features
        features = self.feature_extractor(x)  # (B, 1024, N)
        
        # Global classification
        global_features = torch.max(features, 2)[0]  # (B, 1024)
        global_pred = self.global_classifier(global_features)
        
        # Point-wise classification
        point_pred = self.point_classifier(features)  # (B, num_classes, N)
        point_pred = point_pred.transpose(2, 1)  # (B, N, num_classes)
        
        return global_pred, point_pred

print("PointNet++ C-Section Pile Detection Model defined")
print("Note: This is a simplified version. Full implementation would use")
print("the complete SetAbstraction and FeaturePropagation layers.")